<?php

  ini_set('display_errors', true);
  error_reporting(E_ALL);

  const DEVELOPMENT = false;
  const MAIL_ALL_ERRORS = false;
  const ENVIRONMENT = 'PRODUCTION';
  const DIR_ROOT = '/home/<USER>/public_html/lavri/';
  const LOG_QUERY = true;

  //MAIL
  const MAIL_FROM = "<EMAIL>";
  const MAIL_DEVELOPMENT = "<EMAIL>";
  const MAIL_ERRORS = "<EMAIL>";
  const MAIL_BOUNCE_ADDRESS = "<EMAIL>";

  // DKIM
//  Config::set("DKIM",[
//    '*'=>[
//      'DKIM_DOMAIN'=>'jamwerkt.nl',
//      'DKIM_SELECTOR'=>'jamwerktkey',
//      'DKIM_PRIVATE_KEY'=>""
//    ]
//  ]);

  require_once('gsdfw/includes/vendor/autoload.php');
  require_once('gsdfw/includes/GsdMailer.php');
  require_once('gsdfw/includes/functions.inc.php');


  $email_to = '<EMAIL>';

  $html = '';

  $transport = Swift_SendmailTransport::newInstance('/usr/sbin/sendmail -bs');
  $mailer = Swift_Mailer::newInstance($transport);
  $message = Swift_Message::newInstance();

  $message
    ->setFrom(MAIL_FROM)
    ->setTo($email_to)
    ->setSubject('DKIM signed mail')
    ->setBody($html)
  ;
  // attach dkim key to message headers

  if(Config::isdefined("DKIM")) {
    $domain = substr(MAIL_FROM, strrpos(MAIL_FROM, '@')+1);
    $dkim_config = Config::get("DKIM");
    if(!isset($dkim_config[$domain])) {
      //this domain has no dkim config, fallback wildcard config if possible
      $domain = '*';
    }
    if(isset($dkim_config[$domain])) {
      $dkim_signer = Swift_Signers_DKIMSigner::newInstance($dkim_config[$domain]['DKIM_PRIVATE_KEY'], $dkim_config[$domain]['DKIM_DOMAIN'], $dkim_config[$domain]['DKIM_SELECTOR']);
      $message->attachSigner($dkim_signer);
    }
  }
  $result = $mailer->send($message);

  echo 'SUCCESS!';
