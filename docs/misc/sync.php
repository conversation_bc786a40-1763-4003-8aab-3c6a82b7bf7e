<?php
//check for info:
//http://blogs.oracle.com/jkini/entry/how_to_scp_scp_and
//for generating a private key: http://blogs.oracle.com/jkini/entry/how_to_scp_scp_and

ini_set('max_execution_time', '60000');
if (sizeof($_POST) == 0) {
$options = array(
  //		'host' => 'webservices.upexia.nl',
  'tmpfile' => 'temp/_tmp.tmp',
  'sshuser' => 'gsdwebeu',
  'sshhost' => 'gsdweb.eu',
  'sshport' => '22',
  'serverPath' => '/home/<USER>/public_html/landoll',
  'localmysqlhost' => 'localhost',
  'localmysqluser' => 'root',
  'localmysqlpass' => 'root',
  'localmysqldb' => 'pidz',
  'remotemysqlhost' => '',
  'remotemysqluser' => 'root',
  'remotemysqlpass' => base64_decode('ckRVZVVXRUdFWlNKNXdyVA=='),
  'remotemysqldb' => '',
  'dumpfile' => 'data/_db.db',
  'servers' => array(
    'GSD invoice' => array(
      'sshuser' => 'gsdwebeu',
      'sshhost' => 'vps-7255.firstfind.nl',
      'serverPath' => '/home/<USER>/public_html/gsd',
      'excludes' => array(
        '.svn',
        '*.bak',
        '/.htaccess*',
        '/.buildpath',
        '/.project',
        '/.settings',
        '/logs',
        '/log',
        '/gsdfw/tools',
        '/.cache',
        '/docs',
        '/gsdfw/docs',
        '/projects/gsd/uploads',
        '/projects/gsd/sites',
        '/projects/landoll',
        '/projects/onderdezon',
        '/projects/speelgoed',
        '/projects/tuinberegening',
        '/projects/kvobladel',
        '/projects/amb',
        '/projects/partyxperience',
        '/projects/reclametafel',
        '/projects/achterstehoef',
        '/projects/vhsbladel',
        '/projects/hendor',
        '/projects/iwan',
        '/projects/lavrijsen',
        '/projects/promatec',
        '/projects/hovenier',
        '/projects/rjeans',
        '/projects/vandenborne',
        '/projects/wintermans',
        '/projects/chaletlatirelire',
        '/projects/monkeytree',
        '/projects/vanrijnoldtimers',
        '/projects/autoservicebladel',
        '/projects/jolijnmonteiro',
        '/projects/bouwinitiatief',
        '/projects/dentref',
        '/projects/fixet',
        '/projects/uwbedrijfskeuken',
        '/projects/vakgroephoveniers',
        '/projects/handass',
        '/projects/lavri',
        '/projects/vdlcontainer',
        '/projects/ventilair',
        '/projects/vandermierden',
        '/projects/robotmaaier',
        '/projects/default_rival',
        '/projects/tenvorsel',
        '/projects/default_electro_shoppe',
        '/projects/textielgroep',
        '/projects/earthmineralen',
        '/projects/weddingzandthingz',
        '/projects/nekkermenneke',

        '/temp',
        '*configure*',
        '/downloads',
        '/archive',
        '/google*.html',
        '/beslist*.html',
        '/.idea',
        '/gsdfw/includes/composer.lock',
        '/gsdfw/includes/composer.json',
        '/gsdfw/includes/tfpdf/font/unifont/*.dat',
        '/gsdfw/includes/tfpdf/font/unifont/*.cw127.php',
        '/gsdfw/includes/tfpdf/font/unifont/*.mtx.php',
      ),
    ),
    'GSD landoll' => array(
      'sshuser' => 'gsdwebeu',
      'sshhost' => 'vps-7255.firstfind.nl',
      'serverPath' => '/home/<USER>/public_html/landoll',
      'excludes' => array(
        '.svn',
        '*.bak',
        '/.htaccess*',
        '/.buildpath',
        '/.project',
        '/.settings',
        '/logs',
        '/log',
        '/gsdfw/tools',
        '/.cache',
        '/docs',
        '/gsdfw/docs',
        '/projects/landoll/uploads',
        '/projects/landoll/sites',
        '/projects/tuinberegening',
        '/projects/onderdezon',
        '/projects/speelgoed',
        '/projects/gsd',
        '/projects/kvobladel',
        '/projects/amb',
        '/projects/partyxperience',
        '/projects/reclametafel',
        '/projects/achterstehoef',
        '/projects/vhsbladel',
        '/projects/hendor',
        '/projects/iwan',
        '/projects/lavrijsen',
        '/projects/promatec',
        '/projects/hovenier',
        '/projects/rjeans',
        '/projects/vandenborne',
        '/projects/wintermans',
        '/projects/chaletlatirelire',
        '/projects/monkeytree',
        '/projects/vanrijnoldtimers',
        '/projects/autoservicebladel',
        '/projects/jolijnmonteiro',
        '/projects/bouwinitiatief',
        '/projects/dentref',
        '/projects/fixet',
        '/projects/uwbedrijfskeuken',
        '/projects/vakgroephoveniers',
        '/projects/handass',
        '/projects/lavri',
        '/projects/vdlcontainer',
        '/projects/ventilair',
        '/projects/vandermierden',
        '/projects/robotmaaier',
        '/projects/default_rival',
        '/projects/tenvorsel',
        '/projects/default_electro_shoppe',
        '/projects/textielgroep',
        '/projects/earthmineralen',
        '/projects/weddingzandthingz',
        '/projects/nekkermenneke',

        '/temp',
        '*configure*',
        '/downloads',
        '/archive',
        '/google*.html',
        '/beslist*.html',
        '/.idea',
        '/gsdfw/includes/composer.lock',
        '/gsdfw/includes/composer.json',
        '/gsdfw/includes/tfpdf/font/unifont/*.dat',
        '/gsdfw/includes/tfpdf/font/unifont/*.cw127.php',
        '/gsdfw/includes/tfpdf/font/unifont/*.mtx.php',
      ),
    ),
    'GSD tuinberegening' => array(
      'sshuser' => 'gsdwebeu',
      'sshhost' => 'vps-7255.firstfind.nl',
      'serverPath' => '/home/<USER>/public_html/tuinberegening',
      'excludes' => array(
        '.svn',
        '*.bak',
        '/.htaccess*',
        '/.buildpath',
        '/.project',
        '/.settings',
        '/logs',
        '/log',
        '/gsdfw/tools',
        '/.cache',
        '/docs',
        '/gsdfw/docs',
        '/projects/tuinberegening/uploads',
        '/projects/tuinberegening/sites',
        '/projects/landoll',
        '/projects/onderdezon',
        '/projects/speelgoed',
        '/projects/gsd',
        '/projects/kvobladel',
        '/projects/amb',
        '/projects/partyxperience',
        '/projects/reclametafel',
        '/projects/achterstehoef',
        '/projects/vhsbladel',
        '/projects/hendor',
        '/projects/iwan',
        '/projects/lavrijsen',
        '/projects/promatec',
        '/projects/hovenier',
        '/projects/rjeans',
        '/projects/vandenborne',
        '/projects/wintermans',
        '/projects/chaletlatirelire',
        '/projects/monkeytree',
        '/projects/vanrijnoldtimers',
        '/projects/autoservicebladel',
        '/projects/jolijnmonteiro',
        '/projects/bouwinitiatief',
        '/projects/dentref',
        '/projects/fixet',
        '/projects/uwbedrijfskeuken',
        '/projects/vakgroephoveniers',
        '/projects/handass',
        '/projects/lavri',
        '/projects/vdlcontainer',
        '/projects/ventilair',
        '/projects/vandermierden',
        '/projects/robotmaaier',
        '/projects/default_rival',
        '/projects/tenvorsel',
        '/projects/textielgroep',
        '/projects/earthmineralen',
        '/projects/weddingzandthingz',
        '/projects/nekkermenneke',

        '/temp',
        '*configure*',
        '/downloads',
        '/archive',
        '/google*.html',
        '/beslist*.html',
        '/.idea',
        '/gsdfw/includes/composer.lock',
        '/gsdfw/includes/composer.json',
        '/gsdfw/includes/tfpdf/font/unifont/*.dat',
        '/gsdfw/includes/tfpdf/font/unifont/*.cw127.php',
        '/gsdfw/includes/tfpdf/font/unifont/*.mtx.php',
      ),
    ),
    'Hovenierwinkel.nl' => array(
      'sshuser' => 'gsdwebeu',
      'sshhost' => 'vps-7255.firstfind.nl',
      'serverPath' => '/home/<USER>/public_html/hovenier',
      'excludes' => array(
        '.svn',
        '*.bak',
        '/.htaccess*',
        '/.buildpath',
        '/.project',
        '/.settings',
        '/logs',
        '/log',
        '/gsdfw/tools',
        '/.cache',
        '/docs',
        '/gsdfw/docs',
        '/projects/hovenier/uploads',
        '/projects/hovenier/sites',
        '/projects/landoll',
        '/projects/onderdezon',
        '/projects/speelgoed',
        '/projects/gsd',
        '/projects/kvobladel',
        '/projects/amb',
        '/projects/partyxperience',
        '/projects/reclametafel',
        '/projects/achterstehoef',
        '/projects/vhsbladel',
        '/projects/hendor',
        '/projects/iwan',
        '/projects/lavrijsen',
        '/projects/promatec',
        '/projects/tuinberegening',
        '/projects/rjeans',
        '/projects/vandenborne',
        '/projects/wintermans',
        '/projects/chaletlatirelire',
        '/projects/monkeytree',
        '/projects/vanrijnoldtimers',
        '/projects/autoservicebladel',
        '/projects/jolijnmonteiro',
        '/projects/bouwinitiatief',
        '/projects/dentref',
        '/projects/fixet',
        '/projects/uwbedrijfskeuken',
        '/projects/vakgroephoveniers',
        '/projects/handass',
        '/projects/lavri',
        '/projects/vdlcontainer',
        '/projects/ventilair',
        '/projects/vandermierden',
        '/projects/robotmaaier',
        '/projects/default_rival',
        '/projects/tenvorsel',
        '/projects/default_electro_shoppe',
        '/projects/textielgroep',
        '/projects/earthmineralen',
        '/projects/weddingzandthingz',
        '/projects/nekkermenneke',

        '/temp',
        '*configure*',
        '/downloads',
        '/archive',
        '/google*.html',
        '/beslist*.html',
        '/.idea',
        '/gsdfw/includes/composer.lock',
        '/gsdfw/includes/composer.json',
        '/gsdfw/includes/tfpdf/font/unifont/*.dat',
        '/gsdfw/includes/tfpdf/font/unifont/*.cw127.php',
        '/gsdfw/includes/tfpdf/font/unifont/*.mtx.php',
      ),
    ),
  ),
  'excludes' => array(
    '.svn',
    '*.bak',
    '/.htaccess*',
    '/.buildpath',
    '/.project',
    '/.settings',
    '/logs',
    '/log',
    '/gsdfw/tools',
    '/.cache',
    '/docs',
    '/gsdfw/docs',

    '/temp',
    '*configure*',
    '/downloads',
    '/archive',
    '/gsdfw/includes/tfpdf/font/unifont/*.dat',
    '/gsdfw/includes/tfpdf/font/unifont/*.cw127.php',
    '/gsdfw/includes/tfpdf/font/unifont/*.mtx.php',
  ),
  'userData' => array(
    'data',
    //			'conf/conf.php',
  ),
);
?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
  <title>sync</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <style type="text/css">
    <!--
    html, body {
    }

    textarea, input {
      width: 600px;
    }

    textarea {
      height: 100px;
    }

    input.radio, input.checkbox {
      width: auto;
    }

    -->
  </style>
  <script type="text/javascript" src="/gsdfw/includes/jsscripts/jquery/jquery-1.8.3.min.js"></script>
  <script type="text/javascript">
    $(document).ready(function () {
      $('.serverSelect').bind('change', function () {
        var conf = $.parseJSON($(this).val());
        for (i in conf) {
          $('#' + i).val(conf[i]);
        }
      });
      $('.serverSelect:first').trigger("click");
      $('.serverSelect:first').change();
    });
  </script>

</head>
<body>

<form method="post" action="sync.php">
  <table>
    <tr>
      <td></td>
      <td>
        <label><input type="radio" name="options[direction]" value="up" class="radio" checked="checked"/> up</label>
        <label><input type="radio" name="options[direction]" value="down" class="radio"/> down</label>
      </td>
    </tr>
    <tr>
      <td></td>
      <td>
        <label><input type="radio" name="options[compareType]" value="checksum" class="checkbox" checked="checked"/> checksum (slower but more accurate)</label>
        <label><input type="radio" name="options[compareType]" value="modtime" class="checkbox"/> mod time &amp; size</label>
      </td>
    </tr>
    <tr>
      <td></td>
      <td>
        <label><input type="checkbox" name="options[delete]" value="1" class="checkbox"/> delete non existent remote (BE CAREFUL! DO DRY RUN FIRST!)</label>
      </td>
    </tr>
    <tr>
      <td></td>
      <td>
        <label><input type="checkbox" name="options[includeUserData]" value="1" class="checkbox"/> include user data</label>
      </td>
    </tr>
    <tr>
      <td></td>
      <td>
        <label><input type="checkbox" name="options[database]" value="1" class="checkbox"/> include database</label>
      </td>
    </tr>
    <tr>
      <td></td>
      <td>
        <label><input type="checkbox" name="options[dryRun]" value="1" class="checkbox" checked="checked"/> dry run</label>
      </td>
    </tr>
    <tr>
      <td></td>
      <td>
        <label><input type="checkbox" name="options[debug]" value="1" class="checkbox"/> debug</label>
      </td>
    </tr>
    <tr>
      <td>server</td>
      <td>
        <?php $i = 0;
          foreach($options['servers'] as $servername => $server) { ?>
            <label><input type="radio" name="server" value="<?php echo htmlentities(json_encode($server)) ?>" class="radio serverSelect"<?php if($i++ == 0) { ?> checked="checked" <?php } ?> /> <?php echo $servername ?>
            </label>
          <?php } ?>
      </td>
    </tr>
    <?php foreach($options as $k => $v) {
      if($k == 'servers') {
        continue;
      } ?>
      <tr>
        <td><?php echo $k ?></td>
        <?php if(is_array($v)) { ?>
          <td><textarea name="options[<?php echo $k ?>]" id="<?php echo $k ?>"><?php echo implode(",\n", $v) ?></textarea></td>
        <?php }
        elseif(is_string($v)) { ?>
          <td><input type="text" name="options[<?php echo $k ?>]" id="<?php echo $k ?>" value="<?php echo $v ?>"/></td>
        <?php } ?>
      </tr>
    <?php } ?>
    <tr>
      <td></td>
      <td><input type="submit"/></td>
    </tr>
  </table>
</form>

</body>
</html><?php
  }
  else {
    ob_end_clean();
    header('Content-type: text/plain');
    flush();

    $o = $_POST['options'];
    //	print_r($o);

    touch($o['tmpfile']);
    chmod($o['tmpfile'], 0600);
    file_put_contents($o['tmpfile'], "******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n");

    $baseCmd = 'rsync -avzh';

    if(isset($o['compareType']) && $o['compareType'] == 'checksum') {
      $baseCmd .= ' -c';
    }

    if(isset($o['dryRun'])) {
      $baseCmd .= ' --dry-run';
    }

    $baseCmd .= ' -e \'ssh';
    if(isset($o['sshport']) && $o['sshport'] != '22') {
      $baseCmd .= ' -p ' . $o['sshport'];
    }
    $baseCmd .= ' -i ' . $o['tmpfile'];
    $baseCmd .= ' -o StrictHostKeyChecking=no';
    $baseCmd .= '\' ';
    $baseCmd .= ' --omit-dir-times --no-g --no-o --no-p --chmod=u=rwx,g=rx,o=rx ';
    $baseCmd .= '--progress';

    if(isset($o['delete']))
      $baseCmd .= ' --delete';

    //	print_r($baseCmd);die();

    $sshBase = 'ssh -i data/_tmp.tmp -o StrictHostKeyChecking=no ';
    $sshBase .= sprintf(' %s@%s \'cd %s;%%s;\'', $o['sshuser'], $o['sshhost'], $o['serverPath']);

    if($o['direction'] == 'up') {
      $userData = explode("\n", $o['userData']);
      $excludes = explode(",", $o['excludes']);

      if(!is_array($excludes))
        $excludes = array();

      $excludes[] = '/sync*.php';
      $excludes[] = basename($o['tmpfile']);

      $cmd = $baseCmd;

      /*		if (isset($o['database']) && !isset($o['includeUserData'])) {
      //			$cmd .= sprintf(' --include=\'%s\'', dirname(trim($o['dumpfile'])));
            $cmd .= sprintf(' --include=\'%s\'', trim($o['dumpfile']));
      //			$cmd .= sprintf(' --exclude=\'%s\'', dirname(trim($o['dumpfile'])).'/*');
          }*/

      foreach($excludes as $item)
        $cmd .= sprintf(' --exclude=%s', trim($item));
      if(!isset($o['includeUserData']))
        foreach($userData as $item)
          $cmd .= sprintf(' --exclude=%s', trim($item));

      if(isset($o['database']) && !isset($o['includeUserData'])) {
        $cmd = str_replace(sprintf(' --exclude=%s', dirname(trim($o['dumpfile']))), '', $cmd);
        //			$cmd .= sprintf(' --include=\'%s\'', dirname(trim($o['dumpfile'])));
        $cmd .= sprintf(' --include=\'%s\'', trim($o['dumpfile']));
        $cmd .= sprintf(' --exclude=\'%s\'', dirname(trim($o['dumpfile'])) . '/*');
      }

      $cmd .= sprintf(' . %s@%s:%s/.', $o['sshuser'], $o['sshhost'], $o['serverPath']);

      // dmp database
      if(isset($o['database'])) {
        print 'Dumping database, please wait... '/*."\n"*/
        ;
        flush();
        system('mysqldump -h' . $o['localmysqlhost'] . ' -u' . $o['localmysqluser'] . ' -p' . $o['localmysqlpass'] . ' ' . $o['localmysqldb'] . ' | bzip2 >' . $o['dumpfile']);
        $size = filesize($o['dumpfile']);
        print 'Done (' . round($size / 1048576, 1) . 'MB).' . "\n";
        flush();
        //			die();
      }

      //		$cmd .= ' 2>&1';
      //		print $cmd."\n";
      //		system('ls -al');
      //		shell_exec($cmd);
      // 		die($cmd);
      //		print_r(wordwrap($cmd,60));
      //		die();
      print 'Starting sync. If you included the database, this can take a while...' . "\n";
      flush();
      passthru($cmd/*.' 2>&1'*/);

      // import database
      if(isset($o['database'])) {
        unlink($o['dumpfile']);
        //			$ssh = sprintf($sshBase, 'mysql -h'.$o['remotemysqlhost'].' -u'.$o['remotemysqluser'].' -p'.$o['remotemysqlpass'].' '.$o['remotemysqldb'].' <'.$o['dumpfile'].'');
        $ssh = sprintf($sshBase, 'bzcat ' . $o['dumpfile'] . ' | mysql -h' . $o['remotemysqlhost'] . ' -u' . $o['remotemysqluser'] . ' -p' . $o['remotemysqlpass'] . ' ' . $o['remotemysqldb']);
        system($ssh);
        system(sprintf($sshBase, 'rm ' . $o['dumpfile']));
      }

    }
    if($o['direction'] == 'down') {
      $userData = explode("\n", $o['userData']);
      $excludes = explode("\n", $o['excludes']);

      if(!is_array($excludes))
        $excludes = array();

      $excludes[] = 'sync.php';
      $excludes[] = basename($o['tmpfile']);

      $cmd = $baseCmd;
      if(isset($o['debug']))
        $cmd .= ' -vvvvvvvv';

      foreach($excludes as $item)
        $cmd .= sprintf(' --exclude=%s', trim($item));
      if(isset($o['includeUserData']))
        foreach($userData as $item)
          $cmd .= sprintf(' --include=%s', trim($item));

      if(isset($o['database']) && !isset($o['includeUserData'])) {
        //				$cmd .= sprintf(' --include=%s', trim($o['dumpfile']));
        $cmd .= sprintf(' --include=\'%s\'', dirname(trim($o['dumpfile'])));
        //				$cmd .= sprintf(' --include=\'%s\'', dirname(trim($o['dumpfile'])).'/_*.db');
        $cmd .= sprintf(' --include=\'%s\'', trim($o['dumpfile']));
        $cmd .= sprintf(' --exclude=\'%s\'', dirname(trim($o['dumpfile'])) . '/*');
        //				$cmd .= sprintf(' --include=%s', trim($o['dumpfile']));
        //				$cmd .= sprintf(' --exclude=%s', dirname(trim($o['dumpfile'])));
      }

      $cmd .= ' --exclude=/*';
      $cmd .= sprintf(' %s@%s:%s/. .', $o['sshuser'], $o['sshhost'], $o['serverPath']);
      //		print $cmd;
      //		die();
      if(isset($o['debug']))
        print(wordwrap($cmd)) . "\n\n";

      print 'Starting sync. If you included the database, this can take a while...' . "\n";
      flush();

      // dmp database
      if(isset($o['database'])) {
        $ssh = sprintf($sshBase, 'mysqldump -h' . $o['remotemysqlhost'] . ' -u' . $o['remotemysqluser'] . ' -p' . $o['remotemysqlpass'] . ' ' . $o['remotemysqldb'] . ' | gzip >' . $o['dumpfile'] . '');
        print 'Dumping database remotely, please wait...' . "\n";
        flush();
        system($ssh);
        print 'Done.' . "\n";
        flush();
        //			die($cmd);
      }

      //		print $cmd."\n";
      //		system('ls -al');
      //		shell_exec($cmd);
      //		die($cmd);
      print 'Starting sync (if db is included, there may not be any output for a few minutes below this)...' . "\n";
      flush();
      passthru($cmd);
      print 'Done.' . "\n";
      flush();

      // import database
      if(isset($o['database'])) {
        system(sprintf($sshBase, 'rm ' . $o['dumpfile']));
        //			system('mysql -h'.$o['localmysqlhost'].' -u'.$o['localmysqluser'].' -p'.$o['localmysqlpass'].' '.$o['localmysqldb'].' <'.$o['dumpfile']);
        print 'Importing database locally...' . "\n";
        flush();
        system('zcat ' . $o['dumpfile'] . ' | mysql -h' . $o['localmysqlhost'] . ' -u' . $o['localmysqluser'] . ' -p' . $o['localmysqlpass'] . ' ' . $o['localmysqldb']);
        unlink($o['dumpfile']);
        //			$conn = mysql_connect($o['localmysqlhost'], $o['localmysqluser'], $o['localmysqlpass']);
        //			mysql_select_db($o['localmysqldb'])
      }
    }

    unlink($o['tmpfile']);
  }