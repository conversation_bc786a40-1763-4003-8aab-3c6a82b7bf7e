<?php

  ob_start();
  require_once("../../includes/classes/Config.php");
  require_once("../../includes/classes/Context.php");
  require_once("../../../config/configure.inc.php");
  require_once(DIR_CLASSES . "GsdAutoloader.php");
  require_once(DIR_INCLUDES . "vendor/autoload.php");
  require_once(DIR_INCLUDES . "regex.inc.php");
  __autoloader('Trans');
  require_once(DIR_INCLUDES . "functions.inc.php");
  require_once(DIR_INCLUDES . "logging.inc.php");

  __autoloader("Trans");

  ini_set('display_errors', true);
  error_reporting(E_ALL);

//  define("LOG_QUERY", true);

  $logtime = microtime(true);
  $size = 1000;

  echo "Starting with ".$size." records<br/>";

  for($tel=1;$tel<=$size;$tel++) {
    Searched::add("henk".$tel);
  }
  echo "INSERT: ".number_format((microtime(true)-$logtime),2)." seconden<br/>";
  $logtime = microtime(true);

  $count = Searched::find_all();
  echo "SELECT: ".number_format((microtime(true)-$logtime),2)." seconden, aantal geselecteerd ".count($count)." records<br/>";
  $logtime = microtime(true);

  Searched::delete_by([]);
  echo "DELETE: ".number_format((microtime(true)-$logtime),2)." seconden<br/>";
  echo '<br/>UIGEVOERDE QUERIES:<br/>';
  foreach(DBConn::$lastqueries as $query) {
    echo $query["query"].' - '.round($query["duration"]).'ms<br/>';
  }