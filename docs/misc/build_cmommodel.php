<?php

  const DB_HOST = 'localhost';
  const DB_USER = 'root';
  const DB_PASS = '';
$dbnameinbase ="";
if(isset($argv[2])) {
  $dbname = $argv[2];
}
if(isset($argv[3]) && $argv[3]=="setdbname") {
  $dbnameinbase = $dbname;
}


$mysqli = new mysqli(DB_HOST,DB_USER,DB_PASS,$dbname);
if ($mysqli->connect_error) {
  die('Connect Error (' . $mysqli->connect_errno . ') ' . $mysqli->connect_error);
}
if (!$mysqli->set_charset('utf8mb4')) {
  die('Error loading character set utf8mb4');
}

require('table_meta.php');

$table_meta = new TableMeta($argv[1]);
$db_table = $table_meta->getDbTable();
$fields = $table_meta->getFields();
$not_null_columns = $table_meta->getNotNullColumns();
$null = $table_meta->getNull();
$columns = $table_meta->getColumns();
$columns_without_auto_increment = $table_meta->getColumnsWithoutAutoIncrement();
$prim_keys = $table_meta->getPrimKey();
$supported_types = array('float', 'double', 'decimal', 'enum', 'int', 'tinyint', 'bigint', 'mediumint', 'smallint', 'datetime', 'timestamp', 'time', 'date', 'char', 'text', 'varchar');
$supported_types_with_length = array('float', 'double', 'decimal', 'int', 'tinyint', 'bigint', 'mediumint', 'smallint', 'char', 'varchar');

//input cm beginnen
if(!file_exists('../includes/classes/cm/'.$table_meta->getModelFileName())) {
  $handle = fopen('../includes/classes/cm/'.$table_meta->getModelFileName(), 'w');
  $input = "<?php\n\n";
  $input .= "require_once DIR_CLASSES . 'bm/" . $table_meta->getFileName() . "';\n\n";
  $input .= "class " . $table_meta->getModelName() . " extends " . $table_meta->getBaseName() . " {\n";
  $input .= "\n\n";
  $input .= "}";
  fwrite($handle, $input);
//   echo print_r($input, true);
}

//input om beginnen
if(!file_exists('../includes/classes/om/'.$table_meta->getTableFileName())) {
  $handle = fopen('../includes/classes/om/'.$table_meta->getTableFileName(), 'w');
  $input = "<?php\n\n";
  $input .= "require_once DIR_CLASSES . 'cm/" . $table_meta->getModelFileName() . "';\n\n";
  $input .= "class " . $table_meta->getTableName() . " extends " . $table_meta->getModelName() . " {\n";
  $input .= "\n\n";
  $input .= "}";
  fwrite($handle, $input);
//   echo print_r($input, true);
}

