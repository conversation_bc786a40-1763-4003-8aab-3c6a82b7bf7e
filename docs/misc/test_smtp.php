<?php

  ini_set('display_errors', true);
  error_reporting(E_ALL);

  const DEVELOPMENT = false;
  const MAIL_ALL_ERRORS = false;
  const ENVIRONMENT = 'LOCAL';
  const DIR_ROOT = '/home/<USER>/public_html/lavri/';
  const LOG_QUERY = true;

  //MAIL
  const MAIL_FROM = "<EMAIL>";
  const MAIL_DEVELOPMENT = "<EMAIL>";
  const MAIL_ERRORS = "<EMAIL>";
  const MAIL_BOUNCE_ADDRESS = "<EMAIL>";

  const MAIL_TRANSPORT_TYPE = "SMTP";
  const MAIL_SMTP_SERVER = "smtp.gmail.com"; //smpt.gmail.com
  const MAIL_SMTP_SERVER_PORT = "587"; //465 voor gmail
  const MAIL_SMTP_SERVER_SECURITY = "tls"; //ssl
  const MAIL_SMTP_USERNAME = "";
  const MAIL_SMTP_PASSWORD = "";

  require_once('gsdfw/includes/classes/Config.php');
  require_once('gsdfw/includes/classes/Context.php');
  require_once('gsdfw/includes/vendor/autoload.php');
  require_once('gsdfw/includes/classes/GsdMailer.php');
  require_once('gsdfw/includes/functions.inc.php');

//  $transport = Swift_SmtpTransport::newInstance();
//  $transport->setUsername(MAIL_SMTP_USERNAME);
//  $transport->setPassword(MAIL_SMTP_PASSWORD);
//  $transport->setEncryption(MAIL_SMTP_SERVER_SECURITY);
//  $transport->setHost(MAIL_SMTP_SERVER);
//  $transport->setPort(MAIL_SMTP_SERVER_PORT);
  //$transport->setSourceIp('***********');
  //$transport->setLocalDomain('lavri.nl');
  //$transport->setSourceIp('0.0.0.0');
  //$transport->setAuthMode('LOGIN');

//  $mailer = new Swift_Mailer($transport);

//  $mes = Swift_Message::newInstance();
//  $mes->setSubject("test C");
//  $mes->setBody("inhoud C");
//  $mes->setFrom("<EMAIL>");
//  $mes->setTo("<EMAIL>");
//  $mailer->send($mes);

  $gsdMailer = GsdMailer::build("<EMAIL>", "TEST EMAIL: GSD", 'test message '.$_SERVER['SERVER_NAME']);
  $gsdMailer->send();

  echo 'SUCCESS!';
