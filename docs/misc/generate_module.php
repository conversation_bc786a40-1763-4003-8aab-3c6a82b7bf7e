<?php
  /**
   * Created by PhpStorm.
   * Date: 3-11-2015
   * Time: 13:28
   */

  $modulename = isset($argv[1]) ? strtolower(trim($argv[1])) : "";
  $projectname = isset($argv[2]) ? strtolower(trim($argv[2])) : "";

  if($modulename == "") {
    die('U dient een module naam op te geven.');
  }

  echo "\nGenereren van module: " . $modulename;
  if($projectname != "") {
    echo ' voor project: ' . $projectname;
  }

  $root_dir = dirname(__DIR__) . "/";
  $default_module_root = dirname(__DIR__) . "/";
  if($projectname != "") {
    $root_dir .= 'projects/' . $projectname . '/';
  }
  $root_dir .= 'modules/' . $modulename . '/';
  $default_module_root .= 'modules/' . $modulename . "/";

  if(!file_exists($root_dir)) {
    mkdir($root_dir, 0777, true);
  }

  $actions = $root_dir . 'actions/';
  $templates = $root_dir . 'templates/';
  if(!file_exists($actions)) {
    mkdir($actions, 0777, true);
  }

  if(!file_exists($templates)) {
    mkdir($templates, 0777, true);
  }

  $actions_class = $actions . 'actions.class.php';
  if(!file_exists($actions_class)) {
    $handle = fopen($actions_class, 'w');

    $input = "<?php";
    $input .= "\n\nclass " . strtolower($modulename) . ($projectname != "" ? ucfirst($projectname) : "") . "Actions extends ";
    if($projectname != "" && file_exists($default_module_root)) {
      $input .= $modulename;
    }
    else {
      $input .= "gsd";
    }
    $input .= "Actions {\n";
    $input .= "\n";
    $input .= "\n";
    $input .= "}";

    $writeResult = fwrite($handle, $input);

    if($writeResult === false) {
      echo "\nFout tijdens het aanmaken van de actions.class.php file";
    }
    else {
      echo "\nMappen en bestanden succesvol aangemaakt.";
    }
  }
  else {
    echo "\nMappen en bestanden reeds aanwezig.";
  }