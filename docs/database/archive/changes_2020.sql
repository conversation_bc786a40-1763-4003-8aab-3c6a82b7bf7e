-- update db versie zetten op 3 (bij producten die hierna komen)
-- [UIT CODEBASE]------------ RECLAMETAFEL - LIVE -------------------------------------- RELEASED: 11-02-2019 VERSIE 6.4.3
-- update db versie zetten op 7
-- [NIET RELEASEN]----------- HKPARTYSERVICE - LIVE ------------------------------------ RELEASED: 30-04-2020 VERSIE 6.5.2
-- LET OP: 'npm ci' in map /gsdfw/tools/ i.v.m. nieuwe datepicker flatpickr (npm ci gebruikt de package.lock.json, zodat iedereen dezelfde bestanden heeft)

-- update db versie zetten op 16

-- --[UIT CODEBASE]---------- GRAFIKUS - WANDJE.NL - LIVE ------------------------------ RELEASED: 02-11-2020 VERSIE 6.6.5

-- update db versie zetten op 17
-- Updater draaien: versie 18

-- --[UIT CODEBASE]---------- WEDDINGZANDTHINGZ - LIVE --------------------------------- RELEASED: 22-12-2020 VERSIE 6.7.1


-- LET OP: check releasenotes.md 6.6.8: tailwind.css onderdeel van default backend
-- LET OP: 'npm ci' in map /gsdfw/tools/ check releasenotes.md 6.6.9: tailwind.css backend geupgrade van 1 naar 2

-- LET OP: voortaan gebruiken we de gsdfwUpdater om database queries door te voeren. Zie releasenotes 6.7.1
-- Vanaf hier kun je na release de update versie op automatisch laten staan.
-- LET OP: draai "composer install" in map /gsdfw/includes/. CSV parser library "League\Csv" toegevoegd
-- LET OP: 'npm ci' in map /gsdfw/tools/: simplelightbox available in node_modules https://simplelightbox.com/
-- LET OP: draaien build webpack backend VDL/RDE (projecten met opmerking RUN WEBPACK BACKEND ON RELEASE). Diverse .js verplaatst, verwijderd jquery.easing. Dit is onderdeel van jquery.ui
-- LET OP: 'npm ci' in map /gsdfw/tools/: simple-line-icons moved to node
-- LET OP: draai "composer install" in map /gsdfw/includes/. Updating libraries for PHP 7.4
-- LET OP: upgrade PHP 7.2 naar PHP 7.4. Controleer options. Zie releasnotes 6.7.3. Let op FPDF.
-- LET OP: installeer Composer 2 "composer self-update" en draai "composer install" in map /gsdfw/includes/.
-- Updater draaien: versie 21
-- Updater draaien: FaqPlugin versie 1 (alleen als een project de FAQplugin gebruikt moet deze gedraaid worden. Dit word automatisch gedetecteerd en indien nodig meegenomen bij het draaien van de updater)
-- Updater draaien: versie 22: SiteMessages: berichten voor topheader/popup bovenin website meertalig
-- Updater draaien: versie 23: Varchar gallery item href verbreed voor lange URL's
-- Updater draaien: versie 24: tabellen text/varchar utf8 encoded
-- LET OP: draai "composer install" in map /gsdfw/includes/. Google Authenticator library toegevoegd
-- Updater draaien: versie 25: add category4_id en category5_id on product_container
-- Updater draaien: versie 26: PDF opmerking veld voor bij facturen, vrij te gebruiken veld voor extra tekst op de factuur pdf
-- Updater draaien plugin timeregistration: versie 1: factuur betreft [alleen voor projecten met plugin timeregistration]
-- Updater draaien: versie 27: Procentuele kortingsgroepen maken waaraan producten gekoppeld kunnen worden
-- Updater draaien: versie 28: Site messages afbeelding pop-up
-- LET OP: check releasenotes.md 6.8.2: User::sendLogin();
-- Updater draaien: versie 29: Validate ip-address by email
-- Updater draaien: versie 30: superadmin emailadres <NAME_EMAIL> naar <EMAIL>
-- Updater draaien: versie 31: page.locale removed

-- -------------------------- VENTILAIR - LIVE ----------------------------------------- RELEASED: 25-10-2021 VERSIE 6.8.8
-- -------------------------- COMAIR - LIVE -------------------------------------------- RELEASED: 25-10-2021 VERSIE 6.8.8
-- -------------------------- DENTREF - LIVE ------------------------------------------- RELEASED: 25-10-2021 VERSIE 6.8.8
-- -------------------------- ES-HANDLING/LANDOLL -------------------------------------- RELEASED: 02-05-2022 VERSIE 7.0.5

