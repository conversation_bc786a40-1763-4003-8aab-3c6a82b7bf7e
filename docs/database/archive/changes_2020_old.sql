-- -------------------------- WEDDINGZANDTHINGZ - LIVE --------------------------------- RELEASED: 17-12-2019 VERSIE 6.4.1
-- -------------------------- BINNENTOTAAL - LIVE -------------------------------------- RELEASED: 17-12-2019 VERSIE 6.4.1
-- -------------------------- CHALET-LA-TIRELIRE.nl - LIVE ----------------------------- RELEASED: 17-12-2019 VERSIE 6.4.1
-- -------------------------- AUTOSERVICEBLADEL - LIVE --------------------------------- RELEASED: 20-12-2019 VERSIE 6.4.2 [PLUGINS: cars]
-- -------------------------- KISSCARDS - STAGING -------------------------------------- RELEASED: 23-12-2019 VERSIE 6.4.2
-- -------------------------- KISSCARDS - LIVE ----------------------------------------- RELEASED: 23-12-2019 VERSIE 6.4.2

-- -------------------------- WTCDEKEMPEN - LIVE --------------------------------------- RELEASED: 14-01-2020 VERSIE 6.4.2

-- LET OP: draaien developer script correctPasswords(); (correctie wachtwoord encryptie, zodat deze url friendly zijn. Zorg dat je ingelogd bent voordat je released)

-- 22-01-2020 - ROB - Kortingcodes: omschrijving is een wysiwyg veld maar db veld was een varchar(200)
ALTER TABLE `discount_code` CHANGE `description` `description` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;

-- -------------------------- VOGELS QRCODES - LIVE ------------------------------------ RELEASED: 11-05-2019 VERSIE 6.4.3 [PLUGINS: qrcode]
-- -------------------------- VDH-MACHINES - LIVE -------------------------------------- RELEASED: 11-02-2019 VERSIE 6.4.3
-- -------------------------- RECLAMETAFEL - LIVE -------------------------------------- RELEASED: 11-02-2019 VERSIE 6.4.3
-- -------------------------- MONKEYTREE - LIVE ---------------------------------------- RELEASED: 11-02-2019 VERSIE 6.4.3
-- -------------------------- NEKKERMENNEKE - LIVE - ----------------------------------- RELEASED: 11-02-2019 VERSIE 6.4.3

-- LET OP: draaien "composer install" in map /gsdfw/includes/. Hou even de ivaynberg/select2 in de gaten ivm met upgrade.

-- -------------------------- VANRIJNOLDTIMERS - LIVE ---------------------------------- RELEASED: 11-03-2020 VERSIE 6.4.5
-- -------------------------- REINIERBERENDSEN - LIVE ---------------------------------- RELEASED: 23-03-2020 VERSIE 6.4.6

-- 26-03-2020 - ROBERT - aantal gepickte producten uit warehouse van een bestelling (techwinkel)
ALTER TABLE `order_product_picked` ADD `size_picked_warehouse` MEDIUMINT(8) NOT NULL DEFAULT '0' AFTER `size_picked`;

-- 26-03-2020 - ROBERT - page_option table for code=>value of a page.
CREATE TABLE `page_option` (
    `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
    `page_id` mediumint(8) unsigned NOT NULL,
    `code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
    `value` TEXT NOT NULL,
    PRIMARY KEY (`id`),
    KEY `page_id` (`page_id`),
    KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

-- 20-03-2020 - ROBERT - Communication templates used for example for mail templates, customer can edit his mailtemplates
CREATE TABLE `communication_template` (
     `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
     `code` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
     `locale` VARCHAR(2) NOT NULL DEFAULT 'nl',
     `subject` varchar(150),
     `content` TEXT DEFAULT NULL,
     PRIMARY KEY (`id`),
     KEY `code` (`code`),
     KEY `locale` (`locale`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- 20-03-2020 - ROBERT - registrate to who the template is send. object_id is a flexible reference
CREATE TABLE `communication_template_send` (
                                      `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
                                      `comm_template_id` MEDIUMINT(8) UNSIGNED DEFAULT NULL,
                                      `object_id` MEDIUMINT(8) UNSIGNED DEFAULT NULL,
                                      `user_id` MEDIUMINT(8) UNSIGNED NOT NULL,
                                      `insertTS` datetime NOT NULL,
                                      PRIMARY KEY (`id`),
                                      KEY `comm_template_id` (`comm_template_id`),
                                      KEY `user_id` (`user_id`),
                                      KEY `insertTS` (`insertTS`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
ALTER TABLE `communication_template_send` ADD INDEX(`object_id`);

-- 08-04-2020 - ROBERT - register daily login for login history chart
CREATE TABLE `user_login` (
   `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
   `user_id` MEDIUMINT(8) UNSIGNED DEFAULT NULL,
   `date` date NOT NULL,
   PRIMARY KEY (`id`),
   KEY `user_id` (`user_id`),
   KEY `date` (`date`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- 17-04-2020 - ROB - Optional ability to assign templates to groups
ALTER TABLE `communication_template` ADD `group` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin NULL AFTER `content`;

-- -------------------------- ELEKTROSERVICENIJMEGEN/AREHA - LIVE ---------------------- RELEASED: 20-04-2020 VERSIE 6.5.1
-- -------------------------- ES-HANDLING/LANDOLL -------------------------------------- RELEASED: 20-04-2020 VERSIE 6.5.1
-- -------------------------- ICSCOOLENERGY - LIVE ------------------------------------- RELEASED: 20-04-2020 VERSIE 6.5.1
-- -------------------------- HKPARTYSERVICE - LIVE ------------------------------------ RELEASED: 30-04-2020 VERSIE 6.5.2
-- -------------------------- FIXET - LIVE --------------------------------------------- RELEASED: 11-05-2020 VERSIE 6.5.2

-- 17-04-2020 - ROBERT - functie binnen afdeling
ALTER TABLE `user` ADD `function` VARCHAR(80) NULL COMMENT 'functie binnen afdeling' AFTER `external_id`;

-- 17-04-2020 - ROBERT - maximaal aantal producten in winkelmandje
ALTER TABLE `product` ADD `order_size_max` MEDIUMINT(8) UNSIGNED NULL DEFAULT NULL COMMENT 'max products in basket' AFTER `amount_in_package`;

-- 15-05-2020 - ROB - Give name to templates
ALTER TABLE `communication_template` ADD `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL AFTER `code`;
-- 15-05-2020 - ROB - Code becomes optional, should only be filled by super admin
ALTER TABLE `communication_template` CHANGE `code` `code` varchar(30) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL;
-- 18-05-2020 - ROB - Content per taal in aparte table
CREATE TABLE `communication_template_content` (
     `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
     `communication_template_id` mediumint(8) unsigned NOT NULL,
     `locale` VARCHAR(2) NOT NULL DEFAULT 'nl',
     `subject` varchar(150),
     `content` TEXT DEFAULT NULL,
     PRIMARY KEY (`id`),
     KEY `communication_template_id` (`communication_template_id`),
     KEY `locale` (`locale`),
     CONSTRAINT `communication_template_fkid` FOREIGN KEY (`communication_template_id`) REFERENCES `communication_template` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
-- LET OP: veilige sportvloeren project: eerst deze query draaien:
-- INSERT INTO communication_template_content (`id`, `communication_template_id`, `locale`, `subject`, `content`) SELECT null, id, locale, subject, content FROM communication_template;
ALTER TABLE `communication_template` DROP COLUMN locale, DROP COLUMN subject, DROP COLUMN content;

-- 15-05-2020 - ROB - Register stock change origin
ALTER TABLE `product_stock_change` ADD `origin` VARCHAR(150) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL AFTER `after`;

-- -------------------------- EARTHMINERALEN - STAGING - groothan ---------------------- RELEASED: 08-06-2020 VERSIE 6.5.6
-- -------------------------- EARTHMINERALEN - LIVE - groothan-5 ----------------------- RELEASED: 08-06-2020 VERSIE 6.5.6

-- 13-06-2020 - ROBERT - Factory order type: backorder/standard
ALTER TABLE `factory_order` ADD `type` VARCHAR(20) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'standard' AFTER `supplier_id`;
-- 13-06-2020 - ROBERT - opslaan vanaf welke leveranciersbestelling dit factory_product initieel is besteld.
CREATE TABLE `factory_product_origin` (
      `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
      `factory_product_id` mediumint(8) unsigned NOT NULL,
      `origin_factory_order_id` mediumint(8) unsigned NOT NULL,
      `origin_nr_of_products` mediumint(8) unsigned NOT NULL,
      `moved_nr_of_products` mediumint(8) unsigned NOT NULL,
      `insertTS` DATETIME DEFAULT NULL,
      PRIMARY KEY (`id`),
      KEY `factory_product_id` (`factory_product_id`),
      KEY `origin_factory_order_id` (`origin_factory_order_id`),
      KEY `insertTS` (`insertTS`),
      CONSTRAINT `factory_product_origin_fk` FOREIGN KEY (`factory_product_id`) REFERENCES `factory_product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 22-06-2020 - ROBERT - tooltips are not page specific, but for a complete website
ALTER TABLE page_tooltip DROP FOREIGN KEY fk_page_tooltip;
ALTER TABLE `page_tooltip` DROP `page_id`;
RENAME TABLE page_tooltip TO tooltip;

-- -------------------------- TUINBEREGENING - LIVE ------------------------------------ RELEASED: 24-06-2020 VERSIE 6.5.8

-- LET OP: 'npm ci' in map /gsdfw/tools/ i.v.m. nieuwe datepicker flatpickr (npm ci gebruikt de package.lock.json, zodat iedereen dezelfde bestanden heeft)

-- 06-07-2020 - PAUL - measurement hernoemt naar unit_of_measurement (in gebruik bij Textielgroep)
ALTER TABLE `product` CHANGE `measurement` `unit_of_measurement` VARCHAR(50) NULL DEFAULT 'stuks';

-- -------------------------- VHSBLADEL - LIVE - vhsblade-2 ---------------------------- RELEASED: 07-07-2020 VERSIE 6.6.2 [workbase is teruggedraaid, code is er nog wel. je kunt gewoon releasen met deploy tool]
-- -------------------------- RJEANS - LIVE -------------------------------------------- RELEASED: 07-07-2020 VERSIE 6.6.2 [PLUGINS: qrcode]

-- LET OP: draaien developer script correctImageFilenames(); zie releasenotes v6.6.3.
-- LET OP: htaccess aanpassing doorvoeren bij release. Zie tussen 09-07-2020 in .htaccess

-- -------------------------- VERHALENUITDEKEMPEN - LIVE ------------------------------- RELEASED: 13-08-2020 VERSIE 6.6.3
-- -------------------------- LIVE IWAN------------------------------------------------- RELEASED: 13-08-2020 VERSIE 6.6.3

-- 17-08-2020 - PAUL - product_id NULL toestaan om pdf_upload mogelijk te maken zonder directe link(s)
ALTER TABLE `product_file` MODIFY `product_id` mediumint(8) unsigned NULL;

-- -------------------------- TENVORSEL - LIVE ----------------------------------------- RELEASED: 17-08-2020 VERSIE 6.6.4
-- -------------------------- JOLIJNMONTEIRO - LIVE ------------------------------------ RELEASED: 17-08-2020 VERSIE 6.6.4
-- -------------------------- BLUSHENBROWS - LIVE -------------------------------------- RELEASED: 01-09-2020 VERSIE 6.6.4

-- 31-08-2020 - ROB - Minimale afname per product voor techwinkel
ALTER TABLE `product` ADD `order_size_min` mediumint(8) UNSIGNED NULL COMMENT 'minimale afname' AFTER `amount_in_package`;

-- -------------------------- VAKGROEPHOVENIERS - LIVE --------------------------------- RELEASED: 09-09-2020 VERSIE 6.6.5
-- -------------------------- ZORGBOERDERIJHAPERT - LIVE ------------------------------- RELEASED: 09-09-2020 VERSIE 6.6.5
-- -------------------------- LAVRI - LIVE - lavrinl ----------------------------------- RELEASED: 14-09-2020 VERSIE 6.6.5 [PLUGINS: prospect]
-- -------------------------- ROBOTMAAIER.COM - LIVE ----------------------------------- RELEASED: 16-09-2020 VERSIE 6.6.5 [PLUGINS: faq]
-- -------------------------- FIETSVERHUURBRABANT - LIVE ------------------------------- RELEASED: 25-09-2020 VERSIE 6.6.5
-- -------------------------- VANDERMIERDEN/AKKERZICHT - LIVE -------------------------- RELEASED: 28-09-2020 VERSIE 6.6.5
-- -------------------------- DEBRUIJNZITMEUBELEN - LIVE --------------------------------RELEASED: 07-10-2020 VERSIE 6.6.5
-- -------------------------- BODEL - LIVE --------------------------------------------- RELEASED: 14-10-2020 VERSIE 6.6.5 [Let op: minimalistische database]
-- -------------------------- DESMAAKEERSEL - LIVE ------------------------------------- RELEASED: 28-10-2020 VERSIE 6.6.5
-- -------------------------- PLASTIFRAN - STAGING ------------------------------------- RELEASED: 28-10-2020 VERSIE 6.6.5
-- -------------------------- PLASTIFRAN - LIVE ---------------------------------------- RELEASED: 28-10-2020 VERSIE 6.6.5
-- -------------------------- UWBEDRIJFSKEUKEN.NL - LIVE ------------------------------- RELEASED: 29-10-2020 VERSIE 6.6.5
-- -------------------------- GRAFIKUS - WANDJE.NL - LIVE ------------------------------ RELEASED: 02-11-2020 VERSIE 6.6.5
-- -------------------------- ZORGBOERDERIJBIJDEPINKEN - LIVE -------------------------- RELEASED: 02-11-2020 VERSIE 6.6.5
-- -------------------------- HANDASS - LIVE ------------------------------------------- RELEASED: 02-11-2020 VERSIE 6.6.5
-- -------------------------- ACHTERSTEHOEF - LIVE-------------------------------------- RELEASED: 02-11-2020 VERSIE 6.6.5
-- -------------------------- VENTILAIR - LIVE ----------------------------------------- RELEASED: 02-11-2020 VERSIE 6.6.5
-- -------------------------- COMAIR - LIVE -------------------------------------------- RELEASED: 02-11-2020 VERSIE 6.6.5
-- -------------------------- HENDOR/BOFIL - LIVE -------------------------------------- RELEASED: 02-11-2020 VERSIE 6.6.5
-- -------------------------- THEMOVEMENTCOMPANY.NL - LIVE ----------------------------- RELEASED: 02-11-2020 VERSIE 6.6.5
-- -------------------------- PROMATEC - LIVE ------------------------------------------ RELEASED: 04-11-2020 VERSIE 6.6.5
-- -------------------------- PRAKTIJKTALA - LIVE -------------------------------------- RELEASED: 16-11-2020 VERSIE 6.6.5
-- -------------------------- WINTERMANSBESTRATINGEN - LIVE ---------------------------- RELEASED: 16-11-2020 VERSIE 6.6.5
-- -------------------------- CARPORTSOPMAAT - LIVE ------------------------------------ RELEASED: 16-11-2020 VERSIE 6.6.5

-- 20-11-2020 - ROB - mogelijkheid voor meerdere afbeeldingen per categor   ie
CREATE TABLE `category_image` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `category_id` mediumint(8) UNSIGNED NOT NULL,
  `filename` varchar(255) COLLATE 'UTF8_GENERAL_CI' NOT NULL,
  `type` varchar(30) COLLATE 'UTF8_GENERAL_CI' DEFAULT NULL,
  `insertTS` datetime NOT NULL,
  `updateTS` datetime NOT NULL,
  CONSTRAINT `category_image_id_fk` FOREIGN KEY (`category_id`) REFERENCES `category` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = 'INNODB' COLLATE 'utf8_general_ci';

-- -------------------------- HOLLANDERSHOEVE - LIVE ----------------------------------- RELEASED: 23-11-2020 VERSIE 6.6.7
-- -------------------------- HEBLAD - LIVE -------------------------------------------- RELEASED: 23-11-2020 VERSIE 6.6.7 [PLUGINS: timeregistration]

-- LET OP: check releasenotes.md 6.6.8: tailwind.css onderdeel van default backend

-- -------------------------- GELDROPCENTRUM - LIVE ------------------------------------ RELEASED: 27-11-2020 VERSIE 6.6.8
-- [NIET DEPLOYEN]----------- VDLCONTAINER - LIVE - vdldeale --------------------------- RELEASED: 18-09-2020 VERSIE 6.6.8 [RUN WEBPACK BACKEND ON RELEASE]

-- LET OP: 'npm ci' in map /gsdfw/tools/ check releasenotes.md 6.6.9: tailwind.css backend geupgrade van 1 naar 2

-- -------------------------- TECHWINKEL.NL - STAGING ---------------------------------- RELEASED: 04-12-2020 VERSIE 6.6.9
-- -------------------------- TECHWINKEL.NL - LIVE ------------------------------------- RELEASED: 04-12-2020 VERSIE 6.6.9
-- -------------------------- DENTREF - LIVE ------------------------------------------- RELEASED: 05-12-2019 VERSIE 6.6.9
-- -------------------------- BEDRIJVENMEELOOPDAG - LIVE ------------------------------- RELEASED: 07-12-2020 VERSIE 6.6.9

-- -------------------------- VANDENBORNE - LIVE - ------------------------------------- RELEASED: 07-12-2020 VERSIE 6.7.0
-- -------------------------- JAM - STAGING- ------------------------------------------- RELEASED: 08-12-2020 VERSIE 6.7.0 [PLUGINS: faq]
-- -------------------------- JAM - LIVE - --------------------------------------------- RELEASED: 08-12-2020 VERSIE 6.7.0 [PLUGINS: faq]
-- -------------------------- TEXTIELGROEPHOLLAND.nl - STAGING ------------ ------------ RELEASED: 09-12-2020 VERSIE 6.7.0
-- -------------------------- TEXTIELGROEPHOLLAND.nl - LIVE ---------------------------- RELEASED: 09-12-2020 VERSIE 6.7.0
-- -------------------------- VDHOUTMETAAL - LIVE -------------------------------------- RELEASED: 09-12-2020 VERSIE 6.7.0 [PLUGINS: timeregistration, faq, snelstart]
-- -------------------------- RDE - LIVE ----------------------------------------------- RELEASED: 10-12-2020 VERSIE 6.7.0 [RUN WEBPACK BACKEND ON RELEASE, PLUGINS: timeregistration, faq]
-- -------------------------- GMR - LIVE ----------------------------------------------- RELEASED: 10-12-2020 VERSIE 6.7.0
-- -------------------------- GSD - LIVE ----------------------------------------------- RELEASED: 11-12-2020 VERSIE 6.7.0 [PLUGINS: timeregistration]
-- -------------------------- VDBREINTEGRATIE ------------------------------------------ RELEASED: 14-12-2020 VERSIE 6.7.0
-- -------------------------- LANDSCHOTSEHOEVE - TEST ---------------------------------- RELEASED: 14-12-2020 VERSIE 6.7.0
-- -------------------------- VEILIGESPORTVLOER.COM - LIVE ----------------------------- RELEASED: 14-12-2020 VERSIE 6.7.0
-- -------------------------- VDLCONTAINER - STAGING ----------------------------------- RELEASED: 14-12-2020 VERSIE 6.7.0 [RUN WEBPACK BACKEND ON RELEASE]
-- -------------------------- HEBLAD - LIVE -------------------------------------------- RELEASED: 14-12-2020 VERSIE 6.7.0 [PLUGINS: timeregistration]
-- -------------------------- AMB - LIVE ----------------------------------------------- RELEASED: 16-12-2020 VERSIE 6.7.0
-- -------------------------- WENFDETACHERINGEN - LIVE --------------------------------- RELEASED: 16-12-2020 VERSIE 6.7.0
