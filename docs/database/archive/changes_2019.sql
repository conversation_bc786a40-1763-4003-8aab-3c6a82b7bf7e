ALTER TABLE `product_cont` ADD `sort` MEDIUMINT(8) UNSIGNED NULL AFTER `category3_id`;
-- 08-01-2019 - ROBERT - vergroten veld setting.value
ALTER TABLE `setting` CHANGE `value` `value` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL;

-- 09-01-2019 - ROBERT - page.social_share_date: opslaan datum van delen op social media
ALTER TABLE `page` ADD `social_share_date` DATETIME NULL COMMENT 'Datum waarop gedeeld op sociale media' AFTER `teaser_image_orig`;

-- 25-01-2019 - ROBERT - start en einddatum voor evenementen (PAGE_TYPE_EVENTS)
ALTER TABLE `page` ADD `startdate` DATE NULL COMMENT 'Startdatum van bijv. evenement' AFTER `pagedate`;
ALTER TABLE `page` ADD `enddate` DATE NULL COMMENT 'Einddatum van bijv. evenement' AFTER `startdate`;

ALTER TABLE `organisation_profile` CHANGE `value` `value` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;

-- 01-02-2019 - ROBERT - url_redirect wordt gebruik om oude url 301 te redirecten naar de nieuwe. 4 maanden of oudere redirects worden opgeruimt
CREATE TABLE `url_redirect` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `url_old` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `url_new` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `insertTS` DATETIME NULL,
  PRIMARY KEY (`id`),
  KEY `url_old` (`url_old`),
  KEY `url_new` (`url_new`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
ALTER TABLE `url_redirect` ADD `site_id` MEDIUMINT(8) UNSIGNED NULL AFTER `url_new`, ADD INDEX (`site_id`);

-- 01-02-2019 - ROBERT - custom url's voor categorieën en producten. (heeft ook htacces wijziging, zie blokje onder #31-01-2019)
ALTER TABLE `product_content` ADD `url` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `product_id`;
ALTER TABLE `product_content` ADD INDEX(`url`);
ALTER TABLE `category_content` ADD `url` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `category_id`;
ALTER TABLE `category_content` ADD INDEX(`url`);

-- LET OP: draaien developer script buildCatProdUrls(); (Dit hoeft alleen voor klanten met producten of categorieen ivm custom url's)

-- 06-02-2019 - ROBERT - page table corrections
UPDATE page SET published=0 WHERE published IS NULL;
ALTER TABLE `page` CHANGE `published` `published` BOOLEAN NOT NULL DEFAULT 1;
UPDATE page SET `right`='0' WHERE `right` IS NULL;
ALTER TABLE `page` CHANGE `right` `right` TINYINT(2) UNSIGNED NOT NULL DEFAULT '0';

-- 06-02-2019 - ROBERT - save id of psp (for mollie) to give beter notifications on error
ALTER TABLE `invoice` ADD `paymentmethod_id` VARCHAR(30) NULL COMMENT 'id of psp' AFTER `paymentmethod`;

-- LET OP: /.htaccess aanpassingen, gebruik de laatste .htaccess

-- 18-02-2019 - PAUL - Polish language added
ALTER TABLE `tag` ADD COLUMN `name_pl` varchar(100) NULL AFTER `name_it`;
ALTER TABLE `tag` ADD COLUMN `desc_pl` text NULL AFTER `desc_it`;

-- 18-02-2019 - ROBERT - brand online
ALTER TABLE `brand` ADD `online` BOOLEAN NOT NULL DEFAULT TRUE AFTER `parent_id`;
ALTER TABLE `brand` ADD INDEX(`online`);

-- 05-03-2019 - ROBERT - enlarge setting.value column
ALTER TABLE setting DROP INDEX `value`;
ALTER TABLE setting CHANGE `value` `value` TEXT CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL;

-- 05-03-2019 - ROBERT - product online when signed in as admin
ALTER TABLE `product` ADD `online_admin` BOOLEAN NOT NULL DEFAULT TRUE AFTER `online_custshop`, ADD INDEX (`online_admin`);

-- 13-03-2019 - ROBERT - register searched values for insight
CREATE TABLE `searched` (
  `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
  `keyword` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `insertTS` DATETIME NULL,
  PRIMARY KEY (`id`),
  KEY `insertTS` (`insertTS`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

-- 13-03-2019 - ROBERT - possible to enter keywords on product_content
ALTER TABLE `product_content` ADD `keywords` VARCHAR(255) DEFAULT NULL AFTER `seo_desc`, ADD INDEX (`keywords`);

-- 13-03-2019 - ROBERT - possible to enter keywords on product_cont_container
ALTER TABLE `product_cont_content` ADD `keywords` VARCHAR(255) DEFAULT NULL AFTER `description`, ADD INDEX (`keywords`);

-- 01-04-2019 - ROB - Stond bij textielgroep changes, maar product_cont is een gsdfw tabel
ALTER TABLE `product_cont` ADD `productsheet` VARCHAR(255) NULL AFTER `grammage`;

-- 03-04-2019 - ROB - tabel product_cont kwam niet overeen met BaseProductCont
ALTER TABLE product_cont DROP FOREIGN KEY product_cont_ibfk_2;
ALTER TABLE product_cont DROP `category_id`;
-- 03-04-2019 - ROB - gender en brand_id nullable voor techwinkel
ALTER TABLE `product_cont`
  CHANGE `brand_id` `brand_id` mediumint(8) unsigned NULL AFTER `id`,
  CHANGE `gender` `gender` varchar(8) COLLATE 'utf8_unicode_ci' NULL AFTER `phasingout`;

-- 08-04-2019 - ROB - 2de text veld voor op paginas (voornamelijk voor SEO teksten)
ALTER TABLE `category_content` ADD `description2` text COLLATE 'utf8_general_ci' NULL AFTER `description`;
ALTER TABLE `product_cont_content` ADD `description2` text COLLATE 'utf8_general_ci' NULL AFTER `description`;

-- 11-04-2019 - ROBERT - removed unused property
ALTER TABLE `product` DROP `kg_per_m2`;

-- 11-04-2019 - ROBERT - correction weight. Vdl in kg, others in gramms. Al in gramms from now on.
ALTER TABLE `product` CHANGE `weight` `weight` MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT '0';

-- 18-04-2019 - ROBERT - register searched values ip-adres for filter
ALTER TABLE `searched` ADD `ip` VARCHAR(20) NULL AFTER `keyword`, ADD INDEX (`ip`);

-- 13-03-2019 - ROBERT - category.keywords migrated to category_content.keywords
ALTER TABLE `category_content` ADD `keywords` VARCHAR(255) NULL AFTER `seo_desc`, ADD INDEX (`keywords`);

-- LET OP: draaien developer script migrateCategoryKeywords(); (Dit hoeft alleen voor klanten met tabel category en category_content)

-- 19-04-2019 - ROB - Custom URL's voor product containers (voor techwinkel)
ALTER TABLE `product_cont_content` ADD `url` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL AFTER `product_cont_id`;
ALTER TABLE `product_cont_content` ADD INDEX(`url`);

-- LET OP: draaien "composer install" in map /gsdfw/includes/ : richfilemanager upgrade, alle servers hebben minimaal PHP 7.1

-- 09-05-2019 - ROBERT - Online admin flag category
ALTER TABLE `category` ADD `online_admin` BOOLEAN NOT NULL DEFAULT TRUE AFTER `online_custshop`, ADD INDEX (`online_admin`);

-- LET OP: draaien "composer install" in map /gsdfw/includes/ : upgrade afasconnector
-- LET OP: de productie server dient te zijn ingesteld op PHP 7.2. Controleer dit bij releasen. Stel phpstorm ook in op PHP 7.2 bij PHP language level

-- 14-05-2019 - ROBERT - verwijderen newsletter tabel, word niet meer gebruikt
DROP TABLE IF EXISTS newsletter;

-- 28-05-2019 - ROB - vacancy module verhuist van GSDFW naar JAM, werd te specifiek (en werd niet bij andere projecten gebruikt)
-- LET OP: niet voor JAM
DROP TABLE IF EXISTS vacancy;

-- 04-06-2019 - ROBERT - register searched values also site and lang
ALTER TABLE `searched` ADD `site_id` mediumint(8) DEFAULT NULL AFTER `ip`, ADD INDEX (`site_id`);
ALTER TABLE `searched` ADD `locale` VARCHAR(2) NOT NULL DEFAULT 'nl' AFTER `site_id`, ADD INDEX (`locale`);

-- LET OP: draaien "composer install" in map /gsdfw/includes/ : select2 downgrade 4.0.5

-- 28-05-2019 - ROBERT - extra veld voor invoice_product (op dit moment alleen voor vdl)
CREATE TABLE `invoice_product_option` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `invoice_product_id` mediumint(8) UNSIGNED NOT NULL,
  `code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `value` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`invoice_product_id`,`code`),
  KEY `productid` (`invoice_product_id`),
  KEY `code` (`code`),
  KEY `value` (`value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
ALTER TABLE `invoice_product_option` ADD CONSTRAINT `invoice_product_option_fk_1` FOREIGN KEY (`invoice_product_id`) REFERENCES `invoice_product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 14-06-2019 - ROB - Alternatieve naam voor producten (product containers)
ALTER TABLE `product_cont_content` ADD `alternate_name` VARCHAR(255) DEFAULT NULL AFTER `name`;

-- LET OP: check releasenotes.md 6.2.2
-- LET OP: draaien "composer install" in map /gsdfw/includes/ : swiftmailer upgrade van 5 naar 6

-- 28-06-2019 - ROB - Verpakkingsaantal verkoop, bijv. aantal schroeven in een doos (techwinkel)
ALTER TABLE `product` ADD `amount_in_package` MEDIUMINT(8) NULL DEFAULT NULL AFTER packing_size;

-- 28-06-2019 - ROBERT - kratten te koppelen aan orders
CREATE TABLE IF NOT EXISTS `order_crate` (
    `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
    `order_id` mediumint(8) unsigned DEFAULT NULL,
    `number` smallint(3) unsigned NOT NULL,
    PRIMARY KEY (`id`)
    ,INDEX `order_id` (`order_id`)
    ,UNIQUE `number` (`number`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
ALTER TABLE `order_crate` ADD FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- 09-07-2019 - ROBERT - leveranciersbestelling te verwachten leverdatum (techwinkel)
ALTER TABLE `factory_order` ADD `expected_delivery_date` DATE NULL AFTER `orderdate`;

-- 09-07-2019 - ROBERT - aantal gepickte producten van een bestelling (techwinkel)
CREATE TABLE IF NOT EXISTS `order_product_picked` (
     `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
     `order_id` mediumint(8) unsigned NOT NULL,
     `product_id` mediumint(8) unsigned NOT NULL,
     `size_picked` mediumint(8) unsigned DEFAULT 0 NOT NULL,
     PRIMARY KEY (`id`),
    INDEX `order_id` (`order_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
ALTER TABLE `order_product_picked` ADD FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 10-07-2019 - ROBERT - unique (techwinkel)
ALTER TABLE order_product_picked ADD UNIQUE (`order_id`, `product_id`);

-- 09-07-2019 - ROBERT - aantal producten wat nog naar voorraad moet
ALTER TABLE `factory_product` ADD `to_stock` MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT 0 AFTER `nr_of_products_rec`;

-- 15-07-2019 - ROBERT - meerdere (ean) codes per product
CREATE TABLE IF NOT EXISTS `product_code` (
                                              `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
                                              `product_id` mediumint(8) UNSIGNED NOT NULL,
                                              `code` varchar(40) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
                                              PRIMARY KEY (`id`),
                                              KEY `productid` (`product_id`),
                                              UNIQUE KEY `product_code_id` (`product_id`,`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
ALTER TABLE `product_code` ADD FOREIGN KEY (`product_id`) REFERENCES `product`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- LET OP: draaien "composer install" in map /gsdfw/includes/ : PHPExcel gemigreerd naar PHPSpreadsheet. Zie releasenotes. + Mpdf voor het genereren van PDF bestand van een PHPSpreadsheet

-- 12-08-2019 - ROB - sorteren van product containers
ALTER TABLE `product_cont` ADD `sort` MEDIUMINT(8) UNSIGNED NULL AFTER `category3_id`;

-- 23-08-2019 - ROB - Achteraf betalen optie voor bedrijven
ALTER TABLE `organisation` ADD `postpay` tinyint(1) unsigned NOT NULL DEFAULT '0' AFTER `pay_online`;

-- LET OP: draaien "composer install" in map /gsdfw/includes/ : Guzzlehttp voor eenvoudige api requests

-- 29-08-2019 - ROBERT - Unit4 multivers data
CREATE TABLE IF NOT EXISTS `multivers` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `organisation_id` mediumint(8) UNSIGNED NOT NULL,
  `client_id` varchar(36) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `client_secret` varchar(12) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `authentication_code` text CHARACTER SET utf8 COLLATE utf8_bin,
  `access_token` text CHARACTER SET utf8 COLLATE utf8_bin,
  `access_token_datetime` datetime DEFAULT NULL,
  `token_type` varchar(10) DEFAULT NULL,
  `expires_in` smallint(5) UNSIGNED DEFAULT NULL,
  `refresh_token` text CHARACTER SET utf8 COLLATE utf8_bin,
  `database` VARCHAR(25) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_exactonline_organ_id_idx` (`organisation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

ALTER TABLE `multivers` CHANGE `client_secret` `client_secret` VARCHAR(80) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL;

-- 09-09-2019 - ROBERT - default aangepast
ALTER TABLE `factory_product` CHANGE `nr_of_products` `nr_of_products` MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT '0';
ALTER TABLE `factory_product` CHANGE `nr_of_products_rec` `nr_of_products_rec` MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT '0';
ALTER TABLE `factory_product` CHANGE `processed` `processed` MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT '0';

-- 10-09-2019 - ROBERT - techwinkel
ALTER TABLE `factory_order` ADD `remark` TEXT NULL AFTER `expected_delivery_date`;
ALTER TABLE `factory_product` ADD `to_stock_done` MEDIUMINT(8) NOT NULL DEFAULT '0' AFTER `to_stock`;
ALTER TABLE `product_code` CHANGE `code` `code` VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL;

-- 12-09-2019 - ROBERT - multivers
ALTER TABLE `multivers` DROP authentication_code;

-- 23-09-2019 - ROB - Techwinkel: group id veld, zodat je producten kunt bundelen onder een groep, voor de gecombineerde voorraad
ALTER TABLE `product` ADD `group_id` MEDIUMINT(8) UNSIGNED NULL AFTER `weight`;

-- 27-09-2019 - ROB - Techwinkel: group id veld niet meer nodig
ALTER TABLE `product` DROP `group_id`;

-- 30-09-2019 - ROBERT - Textielgroep: korting over alle producten in een product_cont voor een bepaalde periode
CREATE TABLE `product_cont_discount` (
    `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
    `product_cont_id` mediumint(8) unsigned NOT NULL,
    `discount` decimal(5,2) NOT NULL,
    `startdate` date NOT NULL,
    `enddate` date NOT NULL,
    PRIMARY KEY (`id`),
    KEY `product_cont_id` (`product_cont_id`),
    KEY `startdate` (`startdate`),
    KEY `enddate` (`enddate`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
ALTER TABLE `product_cont_discount` ADD FOREIGN KEY (`product_cont_id`) REFERENCES `product_cont`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 08-10-2019 - PAUL - Textielgroep: Standaard afleveradres
ALTER TABLE organisation_address ADD `is_default_delivery` TINYINT(1) NOT NULL DEFAULT 0 AFTER `is_default`;

-- 15-07-2019 - ROBERT - index op code
ALTER TABLE `product_code` ADD INDEX(`code`);

-- 30-09-2019 - ROBERT - Veiligesportvloer: inschrijven op evenementen
CREATE TABLE `page_event` (
                              `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
                              `page_id` mediumint(8) unsigned DEFAULT NULL,
                              `date` date NOT NULL,
                              `name` varchar(255) DEFAULT NULL,
                              PRIMARY KEY (`id`),
                              KEY `date` (`date`),
                              KEY `name` (`name`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
ALTER TABLE `page_event` ADD FOREIGN KEY (`page_id`) REFERENCES `page`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
CREATE TABLE `page_event_subscription` (
                                           `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
                                           `page_event_id` mediumint(8) unsigned NOT NULL,
                                           `companyname` varchar(255) DEFAULT NULL,
                                           `name` varchar(255) DEFAULT NULL,
                                           `email` varchar(255) DEFAULT NULL,
                                           `phone` varchar(30) DEFAULT NULL,
                                           `address` varchar(30) DEFAULT NULL,
                                           `zipcode` VARCHAR(10) NULL,
                                           `city` varchar(30) DEFAULT NULL,
                                           `persons` smallint(4) DEFAULT NULL,
                                           `remark` TEXT NULL,
                                           `insertTS` DATETIME NOT NULL,
                                           PRIMARY KEY (`id`),
                                           KEY `page_event_id` (`page_event_id`),
                                           KEY `companyname` (`companyname`),
                                           KEY `name` (`name`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
ALTER TABLE `page_event_subscription` ADD FOREIGN KEY (`page_event_id`) REFERENCES `page_event`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- LET OP: draaien composer install in map /gsdfw/includes/ ivm wolfcast/browser-detection + upgrades + remove phpexcel (zie composer_upgrades.txt, deze bevat de upgrade log van composer)

-- 14-11-2019 - ROBERT - Groepering van settings
ALTER TABLE `setting` ADD `type` VARCHAR(30) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL AFTER `id`;

-- 23-11-2019 - PAUL - Mogelijkheid koppelen file aan product (Hendor)
CREATE TABLE `product_file` (
                                `id`  mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT ,
                                `product_id`  mediumint(8) UNSIGNED NOT NULL ,
                                `filename`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
                                `title`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL ,
                                `sort`  int(11) NULL DEFAULT NULL ,
                                PRIMARY KEY (`id`),
                                INDEX `product_id` (`product_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci;

ALTER TABLE `product_file` ADD FOREIGN KEY (`product_id`) REFERENCES `product` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 02-12-2019 - ROBERT - Stock changes log table
CREATE TABLE `product_stock_change` (
                                         `id` mediumint(8) unsigned NOT NULL AUTO_INCREMENT,
                                         `product_id` mediumint(8) unsigned NOT NULL,
                                         `before`INT NOT NULL,
                                         `after` INT NOT NULL,
                                         `insertTS` datetime NOT NULL,
                                         PRIMARY KEY (`id`),
                                         KEY `product_id` (`product_id`),
                                         KEY `insertTS` (`insertTS`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;
