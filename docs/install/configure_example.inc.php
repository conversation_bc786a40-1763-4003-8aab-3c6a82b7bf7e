<?php

  // Static project definition.
//  $project = 'gsd'; // Replace with current project.
//  if($_SERVER["SERVER_PORT"] == 8015) {
//    //force certain domain for port 8015
//    $_SERVER["HTTP_HOST"] = 'beheer.raamdorpel.nl.rde.localhost';
//  }

  // Personal settings.
  const GSD_EMPLOYEE_USERNAME = "ROBERT"; // Your first name in CAPS.
  const DIR_ROOT = '/var/www/html/gsdprojects/'; // Replace with local linux path
  const DOCKER_DIR_ROOT = 'w:/home/<USER>/www/gsdprojects/'; // Replace with local windows/linux path. Used for <PERSON> to create the correct file paths on your local machine.

  if (php_sapi_name() == 'cli') {
    // If called from the commandline. If called from deploy script, $project_name is set.
    $project = (!empty($project_name)) ? $project_name : 'unknown';
    $domain = 'unknown';
  }
  elseif (!str_ends_with($_SERVER['HTTP_HOST'], '.localhost')) {
    // Backwards compatibility with the static hostnames.
    $domain = $_SERVER['HTTP_HOST'];
  }
  elseif (preg_match('/((?P<site_host>.*)\.)?(?P<project>.*)\.localhost$/', $_SERVER['HTTP_HOST'], $matches)) {
    // Example: [portal.hetfruit.nl].[hetfruit].localhost or [project].localhost
    $domain = $matches['site_host'] ?? $_SERVER['HTTP_HOST'];
    $project = $matches['project'];
  }
  else {
    die('Project or site host not found in the URL.');
  }

  // Email.
  $email = "$project@$domain";
  define("MAIL_FROM", $email);
  define("MAIL_DEVELOPMENT", $email);
  define("MAIL_ERRORS", $email);
  define("MAIL_BOUNCE_ADDRESS", $email);
  const MAIL_TRANSPORT_TYPE = "SMTP";
  const MAIL_SMTP_SERVER = "mail";
  const MAIL_SMTP_SERVER_PORT = "1025";
  const MAIL_SMTP_SERVER_SECURITY = "";
  const MAIL_SMTP_USERNAME = "";
  const MAIL_SMTP_PASSWORD = "";
  const MAIL_ALL_ERRORS = false;

  if(file_exists(__DIR__ . '/../projects/' . $project . '/config/configure_LOCAL.inc.php')) {
    // Load local project (database) settings file, if available
    require __DIR__ . '/../projects/' . $project . '/config/configure_LOCAL.inc.php';
  }
  else {
    // Database.
    define('DB_HOST', "mysql");
    define('DB_NAME', $project);
    define('DB_USER', "root");
    define('DB_PASS', "root");
  }

  // Development settings.
  Config::set("MASTER_PASSWORD", "ObiWan#1");
  const DEVELOPMENT = true;
  const ENVIRONMENT = 'LOCAL';
  const LOG_QUERY = true;

  // Load settings file.
  require __DIR__ . '/../projects/' . $project . '/config/configure_root.inc.php';
