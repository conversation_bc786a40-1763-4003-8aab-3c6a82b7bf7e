 --------------------- UPGRADE COMPOSER 14-11-2019 ---------------------
Loading composer repositories with package information
Updating dependencies (including require-dev)
Package operations: 0 installs, 29 updates, 0 removals
  - Updating kylekatarnls/update-helper (1.1.1 => 1.2.0): Downloading (100%)
  - Updating symfony/polyfill-ctype (v1.11.0 => v1.12.0): Downloading (100%)
  - Updating symfony/yaml (v2.8.50 => v2.8.52): Loading from cache
  - Updating symfony/polyfill-php72 (v1.11.0 => v1.12.0): Downloading (100%)
  - Updating symfony/polyfill-mbstring (v1.11.0 => v1.12.0): Downloading (100%)
  - Updating symfony/polyfill-intl-idn (v1.11.0 => v1.12.0): Downloading (100%)
  - Updating symfony/polyfill-iconv (v1.11.0 => v1.12.0): Downloading (100%)
  - Updating doctrine/lexer (1.0.2 => 1.2.0): Downloading (100%)
  - Updating swiftmailer/swiftmailer (v6.2.1 => v6.2.3): Downloading (100%)
  - Updating google/recaptcha (1.2.2 => 1.2.3): Downloading (100%)
  - Updating tedivm/jshrink (v1.3.2 => v1.3.3): Downloading (100%)
  - Updating bootstrap-select/bootstrap-select (v1.13.10 => v1.13.12): Downloading (100%)
  - Updating wyz/practicalafas (2.1 => 2.2): Downloading (100%)
  - Updating tracy/tracy (v2.6.3 => v2.7.1): Downloading (100%)
  - Updating markbaker/matrix (1.1.4 => 1.2.0): Downloading (100%)
  - Updating phpoffice/phpspreadsheet (1.8.2 => 1.9.0): Downloading (100%)
  - Updating myclabs/deep-copy (1.9.1 => 1.9.3): Downloading (100%)
  - Updating psr/log (1.1.0 => 1.1.2): Downloading (100%)
  - Updating mpdf/mpdf (v8.0.2 => v8.0.3): Downloading (100%)
  - Updating ralouphie/getallheaders (2.0.5 => 3.0.3): Downloading (100%)
  - Updating guzzlehttp/psr7 (1.5.2 => 1.6.1): Downloading (100%)
  - Updating guzzlehttp/guzzle (6.3.3 => 6.4.1): Downloading (100%)
  - Updating symfony/process (v3.4.29 => v3.4.35): Downloading (100%)
  - Updating symfony/event-dispatcher-contracts (v1.1.5 => v1.1.7): Downloading (100%)
  - Updating symfony/event-dispatcher (v4.3.2 => v4.3.8): Downloading (100%)
  - Updating symfony/mime (v4.3.2 => v4.3.8): Downloading (100%)
  - Updating symfony/http-foundation (v4.3.2 => v4.3.8): Downloading (100%)
  - Updating doctrine/inflector (v1.3.0 => 1.3.1): Downloading (100%)
  - Installing wolfcast/browser-detection (2.9.3): Downloading (100%)
  - Updating egulias/email-validator (dev-master 950d066 => 2.1.11): Downloading (100%)
  - Removing phpoffice/phpexcel (1.8.1)
Package bootstrap-select/bootstrap-select is abandoned, you should avoid using it. Use snapappointments/bootstrap-select instead.
Writing lock file
Generating autoload files
Carbon 1 is deprecated, see how to migrate to Carbon 2.
https://carbon.nesbot.com/docs/#api-carbon-2
    You can run ".\vendor\bin\upgrade-carbon" to get help in updating carbon and other frameworks and libraries that depend on it.
> ComponentInstaller\Installer::postAutoloadDump
Compiling component files
