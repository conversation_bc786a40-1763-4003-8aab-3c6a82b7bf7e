{"config": {"demo": "default", "debug": false, "compile": {"rtl": {"enabled": false, "skip": []}, "jsUglify": false, "cssMinify": false, "jsSourcemaps": false, "cssSourcemaps": false}, "path": {"src": "./../src", "node_modules": "./node_modules", "core_framework": "./../themes/framework", "demo_api_url": "https://keenthemes.com/metronic/themes/themes/metronic/dist/preview/", "gsdfw": "C:/Work/www/HTTPDOCS/html/gsdprojects/gsdfw"}, "dist": ["C:\\Work\\www\\HTTPDOCS\\html\\metronic_v6.0.0\\dist"]}, "build": {"vendors": {"base": {"src": {"mandatory": {"jquery": {"scripts": ["{$config.path.node_modules}/jquery/dist/jquery.js"]}, "popper.js": {"scripts": ["{$config.path.node_modules}/popper.js/dist/umd/popper.js"]}, "bootstrap": {"scripts": ["{$config.path.node_modules}/bootstrap/dist/js/bootstrap.min.js"]}, "js-cookie": {"scripts": ["{$config.path.node_modules}/js-cookie/src/js.cookie.js"]}, "moment": {"scripts": ["{$config.path.node_modules}/moment/min/moment.min.js"]}, "tooltip.js": {"scripts": ["{$config.path.node_modules}/tooltip.js/dist/umd/tooltip.min.js"]}, "perfect-scrollbar": {"styles": ["{$config.path.node_modules}/perfect-scrollbar/css/perfect-scrollbar.css"], "scripts": ["{$config.path.node_modules}/perfect-scrollbar/dist/perfect-scrollbar.js"]}, "sticky-js": {"scripts": ["{$config.path.node_modules}/sticky-js/dist/sticky.min.js"]}, "wnumb": {"scripts": ["{$config.path.node_modules}/wnumb/wNumb.js"]}, "jquery-validation": {"scripts": ["{$config.path.node_modules}/jquery-validation/dist/jquery.validate.js", "{$config.path.node_modules}/jquery-validation/dist/additional-methods.js", "{$config.path.src}/theme/framework/components/vendors/jquery-validation/init.js"]}}, "optional": {"jquery-form": {"scripts": ["{$config.path.node_modules}/jquery-form/dist/jquery.form.min.js"]}, "animate.css": {"styles": ["{$config.path.node_modules}/animate.css/animate.css"]}, "line-awesome": {"styles": ["{$config.path.src}/theme/framework/vendors/line-awesome/css/line-awesome.css"], "fonts": ["{$config.path.src}/theme/framework/vendors/line-awesome/fonts/**"]}, "flaticon2": {"styles": ["{$config.path.src}/theme/framework/vendors/flaticon2/flaticon.css"], "fonts": ["{$config.path.src}/theme/framework/vendors/flaticon2/font/**"]}, "sweetalert2": {"styles": ["{$config.path.node_modules}/sweetalert2/dist/sweetalert2.css"], "scripts": ["{$config.path.node_modules}/es6-promise-polyfill/promise.min.js", "{$config.path.node_modules}/sweetalert2/dist/sweetalert2.min.js", "{$config.path.src}/theme/framework/components/vendors/sweetalert2/init.js"]}}}, "bundle": {"styles": "{$config.output}/vendors/base/vendors.bundle.css", "scripts": "{$config.output}/vendors/base/vendors.bundle.js", "images": "{$config.output}/vendors/base/images", "fonts": "{$config.output}/vendors/base/fonts"}}, "custom": {"jquery-ui": {"src": {"styles": ["{$config.path.src}/theme/framework/vendors/jquery-ui/jquery-ui.min.css"], "scripts": ["{$config.path.src}/theme/framework/vendors/jquery-ui/jquery-ui.min.js"], "images": ["{$config.path.src}/theme/framework/vendors/jquery-ui/images/*"]}, "bundle": {"styles": "{$config.output}/vendors/custom/jquery-ui/jquery-ui.bundle.css", "scripts": "{$config.output}/vendors/custom/jquery-ui/jquery-ui.bundle.js", "images": "{$config.output}/vendors/custom/jquery-ui/images"}}, "fullcalendar": {"src": {"styles": ["{$config.path.node_modules}/fullcalendar/dist/fullcalendar.css"], "scripts": ["{$config.path.node_modules}/fullcalendar/dist/fullcalendar.js", "{$config.path.node_modules}/fullcalendar/dist/gcal.js"]}, "bundle": {"styles": "{$config.output}/vendors/custom/fullcalendar/fullcalendar.bundle.css", "scripts": "{$config.output}/vendors/custom/fullcalendar/fullcalendar.bundle.js"}}, "gmaps": {"src": {"scripts": ["{$config.path.node_modules}/gmaps/gmaps.js"]}, "bundle": {"scripts": "{$config.output}/vendors/custom/gmaps/gmaps.js"}}, "jquery-flot": {"src": {"scripts": ["{$config.path.node_modules}/jquery-flot/jquery.flot.js", "{$config.path.node_modules}/jquery-flot/jquery.flot.resize.js", "{$config.path.node_modules}/jquery-flot/jquery.flot.categories.js", "{$config.path.node_modules}/jquery-flot/jquery.flot.pie.js", "{$config.path.node_modules}/jquery-flot/jquery.flot.stack.js", "{$config.path.node_modules}/jquery-flot/jquery.flot.crosshair.js", "{$config.path.node_modules}/jquery-flot/jquery.flot.axislabels.js"]}, "bundle": {"scripts": "{$config.output}/vendors/custom/flot/flot.bundle.js"}}, "datatables.net": {"src": {"styles": ["{$config.path.node_modules}/datatables.net-bs4/css/dataTables.bootstrap4.css", "{$config.path.node_modules}/datatables.net-buttons-bs4/css/buttons.bootstrap4.min.css", "{$config.path.node_modules}/datatables.net-autofill-bs4/css/autoFill.bootstrap4.min.css", "{$config.path.node_modules}/datatables.net-colreorder-bs4/css/colReorder.bootstrap4.min.css", "{$config.path.node_modules}/datatables.net-fixedcolumns-bs4/css/fixedColumns.bootstrap4.min.css", "{$config.path.node_modules}/datatables.net-fixedheader-bs4/css/fixedHeader.bootstrap4.min.css", "{$config.path.node_modules}/datatables.net-keytable-bs4/css/keyTable.bootstrap4.min.css", "{$config.path.node_modules}/datatables.net-responsive-bs4/css/responsive.bootstrap4.min.css", "{$config.path.node_modules}/datatables.net-rowgroup-bs4/css/rowGroup.bootstrap4.min.css", "{$config.path.node_modules}/datatables.net-rowreorder-bs4/css/rowReorder.bootstrap4.min.css", "{$config.path.node_modules}/datatables.net-scroller-bs4/css/scroller.bootstrap4.min.css", "{$config.path.node_modules}/datatables.net-select-bs4/css/select.bootstrap4.min.css"], "scripts": ["{$config.path.node_modules}/datatables.net/js/jquery.dataTables.js", "{$config.path.node_modules}/datatables.net-bs4/js/dataTables.bootstrap4.js", "{$config.path.src}/theme/framework/components/vendors/datatables/init.js", "{$config.path.node_modules}/datatables.net-autofill/js/dataTables.autoFill.min.js", "{$config.path.node_modules}/datatables.net-autofill-bs4/js/autoFill.bootstrap4.min.js", "{$config.path.node_modules}/jszip/dist/jszip.min.js", "{$config.path.node_modules}/pdfmake/build/pdfmake.min.js", "{$config.path.node_modules}/pdfmake/build/vfs_fonts.js", "{$config.path.node_modules}/datatables.net-buttons/js/dataTables.buttons.min.js", "{$config.path.node_modules}/datatables.net-buttons-bs4/js/buttons.bootstrap4.min.js", "{$config.path.node_modules}/datatables.net-buttons/js/buttons.colVis.js", "{$config.path.node_modules}/datatables.net-buttons/js/buttons.flash.js", "{$config.path.node_modules}/datatables.net-buttons/js/buttons.html5.js", "{$config.path.node_modules}/datatables.net-buttons/js/buttons.print.js", "{$config.path.node_modules}/datatables.net-colreorder/js/dataTables.colReorder.min.js", "{$config.path.node_modules}/datatables.net-fixedcolumns/js/dataTables.fixedColumns.min.js", "{$config.path.node_modules}/datatables.net-fixedheader/js/dataTables.fixedHeader.min.js", "{$config.path.node_modules}/datatables.net-keytable/js/dataTables.keyTable.min.js", "{$config.path.node_modules}/datatables.net-responsive/js/dataTables.responsive.min.js", "{$config.path.node_modules}/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js", "{$config.path.node_modules}/datatables.net-rowgroup/js/dataTables.rowGroup.min.js", "{$config.path.node_modules}/datatables.net-rowreorder/js/dataTables.rowReorder.min.js", "{$config.path.node_modules}/datatables.net-scroller/js/dataTables.scroller.min.js", "{$config.path.node_modules}/datatables.net-select/js/dataTables.select.min.js"]}, "bundle": {"styles": "{$config.output}/vendors/custom/datatables/datatables.bundle.css", "scripts": "{$config.output}/vendors/custom/datatables/datatables.bundle.js"}}, "jstree": {"src": {"styles": ["{$config.path.node_modules}/jstree/dist/themes/default/style.css"], "scripts": ["{$config.path.node_modules}/jstree/dist/jstree.js"], "images": ["{$config.path.src}/theme/framework/components/vendors/jstree/32px.png", "{$config.path.node_modules}/jstree/dist/themes/default/40px.png", "{$config.path.node_modules}/jstree/dist/themes/default/*.gif"]}, "bundle": {"styles": "{$config.output}/vendors/custom/jstree/jstree.bundle.css", "scripts": "{$config.output}/vendors/custom/jstree/jstree.bundle.js", "images": "{$config.output}/vendors/custom/jstree/images/jstree"}}, "jqvmap": {"src": {"styles": ["{$config.path.node_modules}/jqvmap/dist/jqvmap.css"], "scripts": ["{$config.path.node_modules}/jqvmap/dist/jquery.vmap.js", "{$config.path.node_modules}/jqvmap/dist/maps/jquery.vmap.world.js", "{$config.path.node_modules}/jqvmap/dist/maps/jquery.vmap.russia.js", "{$config.path.node_modules}/jqvmap/dist/maps/jquery.vmap.usa.js", "{$config.path.node_modules}/jqvmap/dist/maps/jquery.vmap.germany.js", "{$config.path.node_modules}/jqvmap/dist/maps/jquery.vmap.europe.js"]}, "bundle": {"styles": "{$config.output}/vendors/custom/jqvmap/jqvmap.bundle.css", "scripts": "{$config.output}/vendors/custom/jqvmap/jqvmap.bundle.js"}}}}, "demo": {"default": {"base": {"src": {"media": ["{$config.path.src}/theme/demo/default/media/**/*.*"], "styles": ["{$config.path.src}/theme/demo/default/sass/style.scss"], "scripts": ["{$config.path.src}/theme/framework/lib/util.js", "{$config.path.src}/theme/framework/lib/app.js", "{$config.path.src}/theme/framework/components/foundation/**/*.js", "{$config.path.src}/theme/framework/components/base/**/*.js", "{$config.path.src}/theme/demo/default/scripts/bundle/layout.js"]}, "bundle": {"styles": "{$config.output}/demo/default/base/style.bundle.css", "scripts": "{$config.output}/demo/default/base/scripts.bundle.js"}, "output": {"media": "{$config.output}/demo/default/media"}}, "skins": {"header": {"src": {"styles": ["{$config.path.src}/theme/demo/default/sass/header/skins/**/*.scss"]}, "output": {"styles": "{$config.output}/demo/default/skins/header/"}}, "brand": {"src": {"styles": ["{$config.path.src}/theme/demo/default/sass/brand/skins/**/*.scss"]}, "output": {"styles": "{$config.output}/demo/default/skins/brand/"}}, "aside": {"src": {"styles": ["{$config.path.src}/theme/demo/default/sass/aside/skins/**/*.scss"]}, "output": {"styles": "{$config.output}/demo/default/skins/aside/"}}}}}, "app": {"custom": {"src": {"styles-by-demo": ["{$config.path.src}/theme/app/custom/**/*.scss"], "scripts": ["{$config.path.src}/theme/app/custom/**/*.js"], "media": ["{$config.path.src}/media/**/*.*"]}, "output": {"styles-by-demo": "{$config.output}/app/custom/", "scripts": "{$config.output}/app/custom/", "media": "{$config.output}/media/"}}, "bundle": {"src": {"scripts": ["{$config.path.src}/theme/app/bundle/**/*.js"]}, "bundle": {"scripts": "{$config.output}/app/bundle/app.bundle.js"}}}}}