<?php

  Config::set("DB_MULTIPLE_HOSTS", [
    'wtc2k10' => ["DB_HOST" => 'srv02.wtcdekempen.nl', "DB_NAME" => 'wtc2k10', "DB_USER" => '?', "DB_PASS" => '?'],
  ]); //25-06-2018 - ROBERT - multiple host configuration. connect to more then one host via the appmodel. Define second, third host etc via this config

  //-----------mail-------------
  const ENVIRONMENT = 'PRODUCTION'; //LOCAL / STAGING / PRODUCTION
  const MAIL_TRANSPORT_COMMAND = '/usr/sbin/sendmail -bs'; //command used for sendmail
  Config::set("LOG_ALL_MAIL_TO_DB", true); //log all mail to database
  Config::set("DKIM", [
    '*' => [
      'DKIM_DOMAIN'      => 'jamwerkt.nl',
      'DKIM_SELECTOR'    => 'jamwerktkey',
      'DKIM_PRIVATE_KEY' => "",
    ],
  ]); //18-12-2018 - ROBERT - meerdere DKIM's kunnen configureren per domeinnaam. * = wildcard, deze dkim word als fallback gebruikt

  //-----------loggin-------------
  const LOG_QUERY = false; //Log all querys tot mysql_log_{date}.log and slow queries to mysql_slow_log_{date}.log (should not be used on production environment because of performance impact)
  const LOG_MAILS_ALL = true; //Log all sens mails to mails_{date}.log
  Config::set("ERROR_HANDLER_JS", [
    "enabled"  => true, //enable js error logging
    "sendmail" => true, //send email to error mailbox
  ]);//21-04-2022 - ROBERT - enabe js error loging/mails

  //-----------gsdfw-------------
  Config::set("GSDFW_PROJECT_EXTENDS", "jam"); //Huidige project kan modules/classes overloaden van ander project
  Config::set("GSDFW_BACKEND_PRIVILIGES_OPEN", ['M_VOIP' => 'voip', 'M_API' => 'api']); //dit zijn de pagina id's welke extern bereikbaar zijn zonder standaard authenticatie id=>name
  Config::set("GSDFW_TRACY_ENABLED", false); //20-09-2018 - ROBERT - mogelijkheid om tracy uit te zetten via config (bijv bij Ajax calls)
  Config::set("SESSION_SAVE_PATH", "/home/<USER>/sessions"); //01-01-2019 - ROBERT - path voor sessie bestandjes. Wanneer het niet mogelijk is om deze te zetten via .htaccess of php.ini. Dit moet een aparte folder zijn op een shared server, ivm opruimen cache door ander vhost
  Config::set("SESSION_GC_MAXLIFETIME", 36000); //01-01-2019 - ROBERT - aantal seconden dat sessie beschikbaar moet blijven. Altijd samen met SESSION_SAVE_PATH gebruiken. Let op: gebruik Cronjobs::executeCleanDirSessions() om sessie bestanden op te ruimen, of via cron bij service provider. + ZET DEZE WAARDE IN DIRETADMIN
  Config::set("GOOGLE_AUTHENTICATOR", [
    "backend" => [
      "enabled"    => true,
      "usergroups" => "ALL", //ALL: all usergroups must validate / array of usergroups to validate
      "remember"   => 14, //false: not enabled, integer: number of days to remember code on this browser
      "issuer"   => "", //optional: name shown in google authenticator app. Persons name is always shown
    ],
  ]); //20-04-2021 - ROBERT - enable google authenticator login. only for backend implemented currently. Don't forget to set CRYPT_SALT_KEY en CRYPT_SALT_IV if not available.
  Config::set("LOGIN_VALIDATE_IPADDRESS", [
    "backend" => [
      "enabled"    => true,
      "usergroups" => "ALL", //ALL: all usergroups must validate / array of usergroups to validate
    ],
  ]);//01-06-2021 - ROBERT - enable IP-addres check. If an unkwown IP-adress tries to login, they have to validate via e-mail. only for backend implemented currently. Don't forget to set CRYPT_SALT_KEY en CRYPT_SALT_IV if not available. There is also an overview screen of allowed IP-addresses available in otherActions
  Config::set("LOGIN_MAIL_OWNER_FIRST_LOGIN", true);//04-02-2022 - ROBERT - mail the owner of this organisation when user logs in for the first time.
  Config::set("DATABASENAME_REPLACE", [
    "heblad_company" => "heblad_company_staging",
  ]);  //13-01-2023 - ROBERT - replace a databasename with another. This is used for projects with multiple databases in combination with staging


  //-----------caching-----------
  Config::set('CACHE_ENABLED', false); //general enable caching (currently only memcache)
  Config::set('CACHE_OM_ENABLED', false); //object model caching
  Config::set('CACHE_LOG_ENABLED', false); //log caching
  Config::set('CACHE_MEMCACHE_HOST', 'localhost'); //memcache server
  Config::set('CACHE_MEMCACHE_PORT', 11211); //memcache port

  //-----------folders-----------
  const DIR_UPLOAD_CAT = DIR_UPLOADS . 'images/catalog/';
  const URL_UPLOAD_CAT = URL_UPLOADS . 'images/catalog/';
  const DIR_UPLOAD_BRAND = DIR_UPLOADS . 'images/brands/';
  const URL_UPLOAD_BRAND = URL_UPLOADS . 'images/brands/';
  const DIR_UPLOAD_PDF = DIR_UPLOADS . 'pdf/';
  const URL_UPLOAD_PDF = URL_UPLOADS . 'pdf/';
  const DIR_SITE = DIR_PROJECT_FOLDER . 'sites/';
  const URL_SITE = URL_PROJECT_FOLDER . 'sites/';
  const DIR_TEMPLATE = DIR_PROJECT_FOLDER . 'templates/';
  const URL_TEMPLATE = URL_PROJECT_FOLDER . 'templates/';
  const DIR_UPLOADS_SITE = DIR_UPLOADS . 'sites/';
  const URL_UPLOADS_SITE = URL_UPLOADS . 'sites/';

  //-----------image sizes-----------
  const IMAGES_THUMB_WIDTH = 170; //product image
  const IMAGES_THUMB_HEIGHT = 170; //product image
  const IMAGES_ORIG_WIDTH = 750; //product image
  const IMAGES_ORIG_HEIGHT = 550; //product image
  const IMAGES_BRAND_THUMB_WIDTH = 170; //brand image
  const IMAGES_BRAND_THUMB_HEIGHT = 100; //brand image
  const IMAGES_BRAND_ORIG_WIDTH = 700; //brand image
  const IMAGES_BRAND_ORIG_HEIGHT = 600; //brand image
  const IMAGES_CAT_ORIG_RESIZE = false; //cat image
  const IMAGES_CAT_THUMB_WIDTH = 160; //cat image
  const IMAGES_CAT_THUMB_HEIGHT = 160; //cat image
  const IMAGES_CAT_ORIG_WIDTH = 690; //cat image
  const IMAGES_CAT_ORIG_HEIGHT = 280; //cat image
  const IMAGES_PAGE_THUMB_WIDTH = 180; //page image
  const IMAGES_PAGE_THUMB_HEIGHT = 180; //page image
  const IMAGES_PAGE_PREVIEW_WIDTH = 700; //page image
  const IMAGES_PAGE_PREVIEW_HEIGHT = 600; //page image
  const IMAGES_PAGE_THUMBPREVIEW_WIDTH = 130; //page image
  const IMAGES_PAGE_THUMBPREVIEW_HEIGHT = 130; //page image
  Config::set('IMAGES_POPUP_WIDTH', 1280); //sitemessage popup image
  Config::set('IMAGES_POPUP_HEIGHT', 720); //sitemessage popup image

  Config::set("FILEMANAGER_IMAGES_MAX_WIDTH", 8000); // max width of images uploaded through the filemanager (higher width image will be scaled down)
  Config::set("FILEMANAGER_IMAGES_MAX_HEIGHT", 8000); // max height of images uploaded through the filemanager (higher height image will be scaled down)

  //-----------algemeen-----------
  Config::set("BEHEER_NAME", "JEANS Design & Communications"); //11-04-2018 - ROBERT - een specifieke naam voor de beheer omgeving
  Config::set("BEHEER_FOOTER_EXTRA", "extrahtml"); //27-08-2018 - ROBERT - beheer omgeving extra footer tekst (bijvoorbeeld link naar privacy voorwaarden)
  Config::set("BEHEER_LANGUAGES_HIDDEN", ["en"]); //19-07-2019 - ROBERT - hide some languages in beheer interface
  const HASH_STRING = '#34tkdfdds';
  const ADMIN_DEFAULT_ID = 2;
  Config::set("countries", [
    'nl' => 'Nederland',
    'be' => 'België',
    'de' => "Duitsland",
    'fr' => "Frankrijk",
    'dk' => "Denemarken",
    'lu' => "Luxemburg",
  ]); //countries possible
  Config::set("MASTER_PASSWORD", '???');
  Config::set("LOGIN_BACKEND_USERGROUPS", [
    'SUPERADMIN',
    'ADMIN',
    'DISTRIBUTEUR',
    'AGENT',
  ]); //usergroups welke mogen inloggen op de backend [USER.USERGROUP,USER.USERGROUP,...]
  Config::set("LOGIN_FRONTEND_USERGROUPS", [
    'PARTICULIER',
    'BEDRIJF',
    'ADMIN',
    'SUPERADMIN',
  ]); //usergroups welke mogen inloggen op de frontend [USER.USERGROUP,USER.USERGROUP,...]
  Config::set('IS_RESPONSIVE', true);//De website is responsive (Bij bv basket zal voor de Resp bestanden gekozen worden)
  Config::set('SITE_HOSTS_ALTERNATES', [
    'nl'    => 'www.registermakelaar.nl',
    'nl-be' => 'www.registermakelaar.be',
  ]); //Deze worden gebruikt voor het aanmaken van verschillende talen in de sitemap
  Config::set('SITE_ROBOTS', [
    'allow'    => [
      'hendor.com' => [],
    ],
    'disallow' => [
      'hendor.de' => ['nl', 'es', 'en', 'fr'],
      'hendor.es' => ['nl', 'de', 'en', 'fr'],
    ],
  ]); //25-05-2016 - ROBERT - configureer robots.txt. De genoemde talen worden ook genegeerd in de google sitemap
  Config::set('PRIVILEGES_CHANGEABLE', ['WERKNEMER' => ['M_CATALOG']]); //25-03-2016 - ROBERT - welke pageIds kunnen worden uitgezet bij een gebruiker van een bepaalde gebruikersgroep
  Config::set('MESSAGECOORDINATOR_ENABLED', false); //20-04-2016 - ROBERT - enable/disable messagecoordinator
  Config::set("MESSAGECOORDINATOR_TYPES", [
    "project",
  ]); //01-03-2017 - ROBERT - zet notifications (messages) aan in bovenin beheer, en definieert welke types.

  Config::set("MESSAGEBIRD", [
    "API_KEY"        => '', //messagebird SMS api key
    "API_DEV_KEY"    => '',//messagebird SMS api key for development (test mode key in messagebird)
    "API_ORIGINATOR" => '0654650888', //messagebird SMS originator. This can be a phonenumber or 11 character length string
  ]);// 23-05-2017 - ROB - messagebird

  Config::set("CRONJOB_MYSQLBACKUP", [
    '00:05', '04:05', '08:05', '12:05', '16:05', '20:05',
  ]);  //13-06-2017 - ROBERT - mysql backup configuration. Times when mysqlbackup is performed. Default is 01:15 and 13:15

  Config::set("GSDPDF_FONT", "Arial"); //12-11-2018 - ROBERT - set default PDF font
  Config::set("IPSTACK", [
    'accesskey' => '?', //acceskey
    'SSL'       => true, //use https
  ]); //20-11-2018 - ROBERT - ipstack config. ipstack is used to dermine country. Use ipstack plugin.

  Config::set("GOOGLE_API_KEYS", [
    "WEBSITE_KEY" => '', //key for public websites (url-restricted)
    "SERVER_KEY"  => '', //key to be used from webservers (ip-restricted)
  ]); //29-10-2019 - ROBERT - google maps api key, used for google maps
  Config::set("HOMEPAGEID", [
    'ADMIN'          => 'M_ORGANISATION_AM',
    'ACCOUNTMANAGER' => 'M_ORGANISATION_BEDRIJF',
    'BEDRIJF'        => 'M_WIZARD',
  ]); //01-05-2020 - ROBERT - openen van een andere pagina na inloggen, afhankelijk van de gebruikersgroep. Standaard word M_HOME geopend.
  Config::set("SOCIAL_SHARE_BUTTONS", [
    "facebook",
    "twitter",
    "linkedin",
    "whatsapp",
    "email",
  ]); //08-07-2020 - ROBERT - social sharebuttons, see site module _socialsharebuttons.php

  const DISABLE_1STLEVEL_CREATENEW = true;
  const DISABLE_1STLEVEL_EDIT = false;
  const DISABLE_1STLEVEL_DELETE = false;
  const DISABLE_1STLEVEL_MOVE = false;
  const PAGEIMAGES_GALLERY = true;
  Config::set("PAGEIMAGES_GALLERY_TITLE", true); //add title field to page gallery images

  const SEOTITLE = false;
  const SEODESCRIPTION = false;
  const TAGS = false;
  const NROFNAVIGATIONLEVELS = 3; //aantal niveaus die per pagina aangemaakt mogen worden
  const IMAGES_EXIF = false; //retrieve lat/long of image
  Config::set('PAGES_MAX_PAGE_COUNT', ['free' => 0, 'standard' => 100, 'large' => 100]); //maximale aantal pagina's aan te maken per abo
  Config::set("MAX_NUMBER_OF_IMAGES", ['standard' => 1000, 'large' => 1000]); //maximaal aantal foto's dat gebruiker zelf mag uploaden over zijn eigen gemaakte pagina's
  Config::set("MAX_SIZE_FILE_DIRECTORY", ['standard' => 20971520, 'large' => 20971520]); //Maximale aantal bytes uploaden aan bestanden. 20mb = 20971520 bytes (exactly)
  Config::set("PAGES_FRONTEND_IMAGES_PER_ROW", 4); //aantal beelden per rij
  Config::set('PAGE_CKEDITOR_TOOLBAR', 'Full'); //toolbar to load ckeditor
  Config::set('PAGE_CKEDITOR_FILEBROWSER', true); //use ckeditor filebrowser
  Config::set('PAGE_CKEDITOR_CONTENT_CSS', []);  //31-03-2019 - Robert - array met stylesheet url's welke worden geladen voor de content van de ckeditor
  Config::set('PAGE_CKEDITOR_ALLOW_SCRIPTS', false);  //31-03-2019 - Robert - mag scripts gebruiken in ckeditor
  Config::set('PAGE_IMAGES_NOT_RESIZE_PAGEIDS', ['site_home']); //paginaids van pagina's waarvan we de large formaat niet aanpassen. Bijv fotoslider pagina
  Config::set('PAGE_IMAGES_CKEDITOR_PAGEIDS', null); //paginaids van pagina's waarvan we de textarea vervangen door de ckeditor
  Config::set('PAGE_IMAGES_URL', ['8']); //24-12-2015 - Robert - pagina id's waarbij op de image pagina ook url's in te voeren zijn OF ALL voor alle pagina's
  Config::set('PAGE_IMAGESGALLERY_URL', ['8']); //24-12-2015 - Robert - pagina's waarbij op de image galerij pagina ook url's in te voeren zijn OF ALL voor alle pagina's.
  Config::set('PAGES_FILES_AUTHORIZE', false); //enable authorization flag bij bestanden
  Config::set('PAGE_IMAGES_CROPPING_TOOL', 'ALL'); //paginaids van pagina's waarvan we de beelden kunnen croppen middels de croppingtool.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_TO_RESIZE', ['ORIG' => 'ORIG']); //Welke formaten er met de tool gecropt mogen worden.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_ORIG', 'ALL'); //'ALL' || pageids OR parent_id. Bij de opgegeven paginaids zal de large(orig) foto worden overschreven na cropping.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_THUMB', 'ALL'); //'ALL' || pageids OR parent_id. Bij d eopgegeven paginaids zal de thumb foto worden overschreven na cropping.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_ORIG_SIZE', ["ALL" => [1140, 300]]); //Breedte, Hoogte. Naar welke aspect-ratio en grootte de large foto gerescaled of gecropt dient te worden.
  Config::set('PAGE_IMAGES_CROPPING_TOOL_RESIZE_TO_THUMB_SIZE', [IMAGES_PAGE_THUMB_WIDTH, IMAGES_PAGE_THUMB_HEIGHT]); //Breedte, Hoogte. Naar welke aspect-ratio en grootte de thumb foto gerescaled of gecropt dient te worden.
  Config::set('PAGES_IMAGES_THUMB_RESIZE', "SQUAREFILL"); //variants: SQUAREFILL: image fully visible as square, SQUARECROP: image cropped into square, DEFAULT: image rescalled proportional
  Config::set("PAGE_TEASER_ENABLED", [123]); //page id || parent_id waarvoor teaser tekst + plaatje ingevoerd kunnen worden || true voor alle pagina's
  Config::set('PAGES_IGNORE_IN_FEED', [123]);//06-09-2016 - ROBERT - pageid's negeren in feed (alleen deze id's niet de kinderen van deze pagina)
  Config::set("PAGE_IMAGES_360_ENABLED", false); //19-09-2016 - Robert - 360 images enabled.
  Config::set('PAGE_CUSTOM_URL', true); //19-09-2015 - Robert - mogelijkheid om een zelf de url in voeren.
  Config::set('PAGE_CANONICAL_URL', false); //15-11-2021 - Paul - mogelijkheid om een in pagina(content) beheer een canonical te selecteren. (HENDOR) LET OP: FRONTEND VERWERKING NOG NIET IN GSDFW
  Config::set("RESTRICT_USER_SITE_ACCESS", [
    3 => [3, 4],
  ]); // use this to limit the sites a user can access from the "select site" or "pages" module: user_id => [site ids which the user may access]

  // set a default module and/or action on a any subpage created under the defined page
  // this can be used to autodirect new subpages to a certain plugin/module/action
  Config::set('DEFAULT_PAGE_MODULE_ACTION', [
    // key = parent id of the page, plugin/module/action will be applied to it's subpages (1 level deep)
    // use like: 2 => ['module' => 'site', 'action' => 'joblist'],
  ]);
  Config::set("SOCIAL_SHARE_ENABLED", true);  //07-01-2019 - ROBERT - enable save and share button
  Config::set('SITE_TOOLTIP_ENABLED', true); //tooltips toepassen en vervangen true OR array[siteid1, siteid2,...]
  Config::set("PAGE_CONTENT_TEMPLATES", true); //08-02-2021 - ROBERT - mogelijkheid om templates te selecteren voor de ckeditor. Deze templates staan op template niveau in de map cktemplates. Zie veiligesportvloer als voorbeeld.
  Config::set("GSDEDITOR", [
    "active"                => true, //is blockeditor active
    "toggle"                => true, //may toggle between old and new editore
    "developer"             => true, //set true if you want to load in dev mode
    "tinymce_stylesheets"   => [
      '/projects/rde/templates/frontend/style/ckeditor_style.css?version=' . time(),
    ], //stylesheets to use in tinymce
    "tinymce_content_style" => 'https://fonts.googleapis.com/css?family=Roboto:300,400,400i,700,900&display=swap', //extra style to use in tinymce
  ]);  //24-02-2022 - ROBERT - blockeditor



  //-----------bestellingen-----------
  const CREDITS_ENABLED = false; //gebruik credit systeem
  Config::set('ORDER_USE_EXISTING_CUST', false); //vlaggetje bestaande klant
  Config::set('ORDER_BESTELBON', false); //toon bestelbon
  Config::set('ORDER_USE_EMAILCUST', false); //mag klant mailen
  Config::set("ORDER_VDWIJST_IMPORT", false); //Pakketlabels import vd Wijst
  Config::set('ORDER_USE_REVISIONNR', false); //revisie nummer gebruikt bij offertes
  Config::set('ORDER_MAY_MAIL', true); //kan offerte direct per mail verzenden
  Config::set('ORDER_HAS_PDF', true); //er is een offerte pdf
  Config::set("ORDER_PAKBON_PDF", true); //18-12-2019 - ROBERT - pakbon in pdf formaat
  Config::set("ORDER_PAKBON_PDF_INVOICE_ADDRESS_SHOW", true); //18-12-2019 - ROBERT - toon factuur adres op pakbon
  Config::set("ORDER_PRINTED_PAKBON", true); //Er kan aangegeven worden of de pakbon al geprint is
  Config::set("ORDER_SPLIT", false); //08-01-2016 - ROBERT - order splitsen knop is zichtbaar, en een order kan worden gesplitst.
  Config::set('ORDER_SEND_INVOICE_STATUS', 'payed'); //na verzenden direct factuur op betaald zetten.
  Config::set('ORDERS_STATI', [
    'new'        => "Nieuw",
    'tendersend' => "Offerte verzonden",
    'ordered'    => "Bestelling",
    'paybefore'  => "Voorgefactureerd",
    'tosend'     => "Te verzenden",
    'send'       => "Verzonden",
    'cancelled'  => "Geannuleerd",
  ]); //mogelijke order statussen
  Config::set('ORDERS_TYPES', [
    'spareparts' => "Spareparts",
    'service'    => "Service",
    'garantie'   => "Garantie",
  ]); //01-06-2016 - ROBERT - defineer mogelijke types van orders
  Config::set("ORDER_EMAIL_FROM_DEFAULT", true); //verstuurd email van EMAIL_FROM, en niet met afzender emailadres eigenaar
  Config::set("ORDER_EMAIL_CC", []); //15-11-2019 - ROBERT - array CC emails bij Order Email, overschrijft standaard cc van owner_user->email
  Config::set("ORDER_EMAIL_BCC", []); //15-11-2019 - ROBERT - array BCC emails bij Order Email
  Config::set("ORDER_MESSAGES_SAVEPDF", true); //26-05-2016 - ROBERT - sla vezonden bestanden op in archief bij order_message

  //-----------invoice-----------
  Config::set('INVOICE_FROM_USER_SHOW', true); //Toon wel/niet filter en kolom van bij facturen overzicht.
  
  Config::set("INVOICE_SEND_ATTACHMENTS_ENABLED", true); //mag bestanden toevoegen bij email pagina facturen
  Config::set("INVOICE_ATTACHMENTS_DEFAULT", true); //standaard beschikbare bestanden
  Config::set('INVOICE_TYPE_DEFAULT', 'invoice'); //standaard factuurtype bij nieuwe factuur
  Config::set("INVOICE_PRODUCT_DECIMALS", 4); //01-12-2015 - ROBERT - product exclusief prijzen aantal decimalen
  Config::set("INVOICE_ROUND_VAT_ROW", '2016-12-19');  //date|false - 19-12-2016 - ROBERT - datum: afronden BTW per productregel vanaf datum, false: afronden na sommatie
  Config::set("INVOICE_FILTER_EMPLOYEE_ENABLED", true); //22-03-2016 - KOEN - filteren op werknemer
  Config::set("INVOICE_FILTER_EXTERNAL_ENABLED", false); // 10-01-2023 - ROB - filteren op external_id (of factuur wel/niet niet naar boekhoudpakket is verzonden)
  Config::set("INVOICE_SEND_READ_RECEIPT_ENABLED", true); //01-06-2016 - KOEN - optie leesbevestiging bij versturen factuur via email
  Config::set("INVOICE_SEND_INVOICE_EMAIL_ONLY", true); //04-11-2016 - ROBERT - alleen facturen naar invoice_email, niet andere adressen
  Config::set("INVOICE_ENABLE_DESCRIPTION_QUICKLIST", true); //true|false - enables invoice description quicklist with autocompletion in Description table.
  Config::set("INVOICE_ENABLE_ORGAN_AND_USER_EDIT", true); //true|false enables changing organ and user details before invoicing an invoice
  Config::set("INVOICE_ENABLE_BATCH_INVOICE", true); //true|false: het kunnen factureren van meerdere facturen tegelijkertijd.
  Config::set("INVOICE_OWN_INVOICENR", true); //true|false: klant voert zelf uniek factuurnummer in (VDL)
  Config::set("INVOICE_MAY_INVOICE_INACTIVE", true); //true|false: mag inactieve klant factureren
  Config::set("INVOICE_LIST_SHOW_EXCL", true);  //true|false: toon exclusief bedrag in facturen lijst
  Config::set("INVOICE_SHOW_PRINT", true);  //true|false: mogelijk pdf zonder briefpapier te bekijken en downloaden.
  Config::set("INVOICE_VARIANTS", ["glas" => "Glasbewassing", "schoonmaak" => "Schoonmaak", "NULL" => "Overige"]); //19-11-2016 - ROBERT - mogelijkheid om varianten (groepering) te gebruiken bij facturen
  Config::set("INVOICE_REMINDER_DATES", [
    1 => 14, //dagen na factuurdatum + betalingstermijn
    2 => 14, //dagen na 1e herinnering
    3 => 14, //dagen na 2e herinnering
    4 => 5, //dagen na 3e herinnering
  ]); //10-12-2016 - ROBERT - herinneringsdatum aanmaningen
  Config::set("INVOICE_OPTIONS", [
    'cost_hours'    => "Kosten uren",
    'cost_products' => "Kosten producten",
  ]); //10-12-2016 - ROBERT - definieer mogelijke invoice_options
  Config::set("INVOICE_PRODUCT_OPTIONS", [
    'serial' => "Serienummer",
  ]); //11-06-2019 - ROBERT - definieer mogelijke invoice_product_options

  //-----------organisation-----------
  Config::set("organisation_types_usg", [
    'ADMIN'        => ['DISTRIBUTEUR', 'AGENT', 'BEDRIJF', 'PARTICULIER'],
    'DISTRIBUTEUR' => ['AGENT', 'BEDRIJF', 'PARTICULIER'],
    'AGENT'        => ['BEDRIJF', 'PARTICULIER'],
  ]); //welke organisatie.types mag een usergroup aanmaken [USER.USERGROUP=>[ORGANISATION.TYPE,ORGANISATION.TYPE,...]]
  Config::set("usergroups_for_organtype", [
    'OTHER'        => ['ADMIN'],
    'DISTRIBUTEUR' => ['DISTRIBUTEUR'],
    'AGENT'        => ['AGENT'],
    'BEDRIJF'      => ['BEDRIJF'],
    'PARTICULIER'  => ['PARTICULIER'],
  ]); //welke gebruikersgroepen kan een bepaald bedrijfstype hebben [ORGANISATION.TYPE=>[USER.USERGROUP,USER.USERGROUP,...]]
  Config::set('ORGANISATION_EMAIL_OBLIDGED', false); //organisatie is verplicht emailadres in te vullen
  Config::set('ORGANISATION_NAME_OBLIDGED', true); //organisatie is verplicht bedrijfsnaam in te vullen
  Config::set('USER_MAY_SEE_OWN_ORGANISATION', true); //ingelogde gebruiker kan eigen organisatie in organisatielijst zien
  Config::set('USER_ADMIN_MAY_SEE_ALL', true); //ADMIN mag alles zien behalve SUPERADMIN
  Config::set("ORGAN_CUST_NR_ENABLED", true); //klantspecifiek klantnummer mogelijk.
  Config::set("USER_LIST_SHOW_CUSTNR", true); // 13-06-2017 - ROB - Tonen van klantnummer op gebruiker overzicht
  Config::set("USER_LIST_SHOW_DELETE", false);  //28-01-2022 - ROBERT - delete organisation on userlist. Default this is possible
  Config::set("ORGAN_EDIT_SHOW_SOCIAL_MEDIA", false); //true/false. Toont Social media inputvelden bij bewerken organisatie.
  Config::set('ORGANISATION_LANGUAGES', [
    'nl' => 'Nederlands',
    'en' => 'Engels',
    'de' => 'Duits',
    'fr' => 'Frans',
  ]); //definieer selecteerbare talen in organisation edit.
  Config::set("ORGANISATION_SHOW_OWNER", false); //19-01-2016 - ROBERT - organisatie eigenaar kunnen aanpassen: true | false | array met gebruikersgroepen
  Config::set("ORGANISATION_SHOW_FAX", false); //19-01-2016 - ROBERT - organisatie fax kunnen aanpassen
  Config::set("ORGANISATION_EDIT_SHOW_VAT_NUMBER", false); //05-04-2016 - KOEN - organisatie btw nummer zichtbaar
  Config::set("ORGANISATION_EDIT_SHOW_COC_NUMBER", ["BEDRIJF"]); //05-04-2016 - KOEN - Organisatie kvk nummer zichtbaar
  Config::set("ORGANISATION_EDIT_SHOW_BIC", false); //false|true|array() - 05-04-2016 - KOEN - toon bic voor niemand (false), iedereen (true), custom (array)
  Config::set("ORGANISATION_EDIT_SHOW_IBAN", false); //false|true|array() - 05-04-2016 - KOEN - toon iban voor niemand (false), iedereen (true), custom (array)
  Config::set("ORGANISATION_EDIT_SHOW_NAME_OF", false); //false|true|array() - 05-04-2016 - KOEN - toon ten name van voor niemand (false), iedereen (true), custom (array)
  Config::set("ORGANISATION_EDIT_SHOW_BANK_NAME", false); //false|true|array() - 05-04-2016 - KOEN - toon bank voor niemand (false), iedereen (true), custom (array)
  Config::set("ORGANISATION_EDIT_SHOW_EXTENSION", false); //26-03-2018 - ROBERT - organisatie adres extensie zichtbaar/word gebruikt

  Config::set("ORGANISATION_INTRACOMMUNAUTAIR", true); //01-01-2016 - TWAN - geen btw voor buitenlandseverzendingen organisatie (no_vat)
  Config::set('ORGANISATION_DEFAULT_COUNTRY', ''); // 24-02-2016 - ROBERT - default country en country_invoice (leeg = geen standaard waarde, klant selecteerd)
  Config::set('ORGANISATION_DEFAULT_LANGUAGE', ''); // 03-03-2016 - ROBERT - default lanuage (leeg = geen standaard waarde, klant selecteerd)
  Config::set("ORGANISATION_INVOICE_ADDRESS_NAME_ENABLED", true); //25-03-2016 - KOEN - factuur adres bedrijfnaam kunnen invullen
  Config::set("ORGANISATION_INVOICE_ADDRESS_CONTACT_NAME_ENABLED", true); //25-03-2016 - KOEN - factuur adres contact persoon kunnen invullen
  Config::set("ORGANISATION_ADDRESS_DEFAULT_TYPE", "delivery"); //31-03-2017 - ROBERT - default organisation_address heeft staandaard dit type (niet gebruikt dan delivery)
  Config::set("ORGANISATION_PROSPECT_ENABLED", true); //true | false. Toon Filter "Prospect" bij relatie lijst en vinkje "Prospect" bij wijzigen organisatie
  Config::set("ORGANISATION_PROFILE", ['mollie' => 'Online betalen Mollie',]); //22-06-2016 - ROBERT - organisation profile properties
  Config::set("ORGAN_EDIT_SHOW_MAP", true); //toont google maps kaart bij organisatie
  Config::set("ORGANISATION_SPECIFIC_PAYMETHODES", [
    'online'   => true, // online betalen via paymentprovider, bijvoorbeeld mollie
    'rekening' => true, // op rekening betalen, dit is vooruit betalen
    'incasso'  => true,
    'postpay'  => true, // op rekening betalen, dit is achteraf betalen
    'cash'     => true, // contant bij afhalen
  ]); //27-03-2017 - ROBERT - bij een organisatie kunnen betaalmethodes worden uit en aangezet.
  Config::set("ADMIN_UPLOAD_ORGAN_LOGO", true); //15-08-2018 - ROB - company logo can be added/removed from relations
  Config::set("ORGANISATION_LOGO_SHOW_HEADER", true); //05-09-2018 - ROBERT - show company logo in header in beheer.
  Config::set("ORGANISATION_WEBSITE_VALIDATE", false); //04-05-2020 - ROBERT - validate website (default is that if a website is not empty, it wil be validated)
  Config::set("ORGANISATION_SOURCE_SELECT", [
    'ORGAN'         => 'Via de Organisatie',
    'CONTACTPERSON' => 'Via de contactpersoon zelf',
    'INTERNET'      => 'Via internet',
  ]); //04-05-2020 - ROBERT - via deze optie word user bron een select box met de gefinieerde opties
  Config::set("ORGANISATION_PURPOSE_SELECT", [
    'ORGAN'         => 'Analyse van kantoren',
    'CONTACTPERSON' => 'Marketing',
  ]); //04-05-2020 - ROBERT - via deze optie word user doel een select box met de gefinieerde opties

  //-----------user-----------
  Config::set("usergroups_for_usg", [
    'ADMIN'        => ['ADMIN', 'DISTRIBUTEUR', 'AGENT', 'BEDRIJF', 'PARTICULIER'],
    'DISTRIBUTEUR' => ['AGENT', 'BEDRIJF', 'PARTICULIER'],
    'AGENT'        => ['BEDRIJF', 'PARTICULIER'],
  ]); //welke gebruikersgroepen mag een bepaalde gebruikersgroep zien/bewerken [USER.USERGROUP=>[USER.USERGROUP,USER.USERGROUP,...]]
  Config::set("USE_ORGAN_IS_CUST_WEBSHOP", false); //Vinkje is webshop klant bij organisaties
  Config::set("USER_INITIALS_SHOW", false); //true|false|array - 05-04-2016 - KOEN - toon initialen
  Config::set('USER_BIRTHDATE_SHOW', false); //06-01-2015 - ROBERT - toon geboortedatum in bewerk schermen
  Config::set("USER_PLACE_OF_BIRTH_SHOW", true); //true|false|array - 04-04-2016 - KOEN - toon geboorte plaats in bewerk schermen
  Config::set("USER_MARITAL_STATUS_SHOW", true); //true|false|array - 04-04-2016 - KOEN - toon burgerlijke staat
  Config::set('USER_EMAIL_NOTUNIQUE_PW', false); //01-05-2014 - ROBERT - dubbele emailadressen zijn mogelijk bij user welke kan inloggen, echter met een uniek wachtwoord
  Config::set('USER_LIST_DIRECTLOGIN', false); //26-11-2015 - ROBERT - toon direct login knoppen.
  Config::set('USER_LIST_DIRECTLOGIN_BLANK', true); //08-11-2019 - ROBERT - op en direct login in nieuwe tab
  Config::set('USER_ADDRESS', ['show' => false, 'oblidged' => false]); //user adress is zichtbaar en/of verpicht
  Config::set('USER_SHOW_NEWSLETTER', true); //nieuwsbrief kunnen aanmelden
  Config::set("USER_USERNAME_ENABLED", true); //31-03-2016 - KOEN - Gebruikers kunnen een username invoeren
  Config::set("USER_USERNAME_USERGROUPS_LOGIN", []); //31-03-2016 - KOEN - Welke gebruikersgroepen mogen inloggen met hun username
  Config::set("USER_PASSWORD_ENCRYPT", true); //01-04-2016 - KOEN - stores encrypted passwords in DB. Let op gebruikt HASH_STRING config.
  Config::set("USER_VOID_ON_DESTROY", true); //12-04-2016 - KOEN - mogelijkheid om void te gebruiken bij het verwijderen, ipv verwijderen uit db
  Config::set("USER_DEFAULT_MAYLOGIN", 0); //01-03-2016 - ROBERT - standaard waarde maylogin bij aanmaken nieuwe user
  Config::set('USER_ADDRESS_SHOWMAP', true); // toon google maps kaart bij werk/afleveradressen
  Config::set('USER_MAIL_ON_CHANGE', true); // mail wanneer gebruiker of organisatie wijzigd
  Config::set('USER_DEPARTMENTS', false); //18-10-2016 - ROBERT - array|false departments zijn selecteerbaar met selectbox per usergroup, wanneer true dan een opentekst veld
  Config::set('USER_FUNCTION', false); //04-05-2020 - ROBERT - functie binnen bedrijf
  Config::set('USER_LIST_SHOW_FUNCTION', false); //04-05-2020 - ROBERT - toon functie in personenlijst
  Config::set("USER_DEPARTMENTS", false); //gebruik afdeling op persoon bewerk scherm
  Config::set('USER_DEFAULT_SOURCE', "Aanvraag offerte/bestelling"); //23-05-2018 - ROBERT - standaard tekst in user.source (bron persoons gegevens)
  Config::set('USER_DEFAULT_PURPOSE', "Zie privacy voorwaarden"); //23-05-2018 - ROBERT - standaard tekst in user.purpose (doel gebruik persoons gegevens)
  Config::set("USER_SEX_IS_NOT_DEFINED", false); // if the user sex is not defined, this can be used to not show gender specific salutions because all sex by default is male (M)
  Config::set('PASSWORD_RESET_LINK_VALID_FOR_MINUTES', 245); // The password reset mail link is valid for this number of minutes. Default is 245 minutes (4 hours + 5 minutes)

  //-----------merken-----------
  const CATALOG_BRAND = false; //producten aan merken koppelen
  Config::set("BRAND_USE_SUB_CATEGORIES", true); //true/false men kan subcategorieën toevoegen bij Merken, en deze koppelen bij de catalogus
  Config::set('CATALOG_BRAND_OBLIDGED', false); //merk verplicht invoeren bij product bewerken

  //-----------categorieen-----------
  const CONFIGURE_ADDPRODUCTS_IN_ROOTDIR = false; //Voeg producten toe aan de root.
  const CONFIGURE_MAX_DEPTH_NESTED_CATS = 1; //Maximaal genestte niveau van categorieën.
  const CONFIGURE_ADD_SAME_PRODUCT_MULTIPLE_CATS = false; //Hetzelfde product onder meerdere categorieën hangen.
  Config::set('CONFIGURE_ADD_PRODUCT_WITHOUT_PRICE', false); //28-05-2018 - PAUL - Product hoeft geen prijs te hebben.
  Config::set('PRODUCT_PRICE_ON_REQUEST', false); //15-06-2022 - RICHARD - Product price on request visible in product prices tab
  Config::set("catalog_languages", ['nl', 'en', 'de', 'fr']); //languages for catalog/product descriptions
  Config::set("CATEGORY_HAS_PDF", false); //07-01-2016 - ROBERT - er kan een PDF worden geupload bij een categorie
  Config::set('USE_CATEGORY_KEYWORDS', false); //true/false : Men kan keywords invoeren welke woorden er gemapt moeten worden bij de import feed van producten. bijv: maaier komt in categorie grasmaaier
  Config::set("CATALOG_LIST_SUPPLIER_SHOW", false); //Toon leverancierscode in product lijst overzicht catalogus en producten
  Config::set('CATEGORY_CUSTOM_URL', true); //31-01-2019 - ROBERT - mogelijkheid om een zelf de categorie url in te voeren
  Config::set("CATEGORY_USE_ONLINE_UC", true); //07-03-2019 - ROBERT - gebruik van online_uc vlag
  Config::set("CATEGORY_USE_ONLINE_ADMIN", true); //07-03-2019 - ROBERT - gebruik van online_admin vlag
  Config::set("CATEGORY_IMAGES_MULTIPLE", true); // 20-11-2020 - ROB - Meerdere afbeeldingen bij categorieen


  //-----------producten-----------
  const TAGS_PRODUCT = false;
  const CATALOG_BUY_DISCOUNT = true; //inkoopkorting aangeven
  Config::set('FRONTEND_PAGER_SIZE', 24); //aantal producten per pagina
  Config::set('CATALOG_PRODUCT_BTWGROUP', [
    'nl' => [1 => 6, 3 => 9, 2 => 21],
    'be' => [1 => 21, 3 => 21, 2 => 21],
  ]); //producten aan btwgroep kunnen koppelen
  Config::set('PRODUCT_SELL_SIZE', false); // Aantal in verpakking (verpakkingsaantal verkoop): prijs word vermenigvuldigd met dit bedrag in mandje
  Config::set('PRODUCT_AMOUNT_IN_PACKAGE', false); // Aantal in verpakking (verpakkingsaantal verkoop): staat los van prijs
  Config::set('PRODUCT_ORDER_SIZE_MAX', false); // 07-05-20120 - ROBERT - Maximaal aantal producten in 1 keer te bestellen in mandje (TE IMPLEMENTEREN IN STANDAARD BASKET)
  Config::set('PRODUCT_RESELLING', false); //Producten kunnen gereselled worden.
  Config::set("PRODUCT_IMAGES_WATERMARK", false); //Gebruik watermerk op product beelden.
  Config::set("PRODUCT_WATERMARK_LARGE", DIR_PROJECT_FOLDER . 'templates/frontend/images/watermark_large.png'); //DIR large watermerk
  Config::set("PRODUCT_WATERMARK_THUMB", DIR_PROJECT_FOLDER . 'templates/frontend/images/watermark_thumb.png'); //DIR thumb watermerk
  Config::set("PRODUCT_YOUTUBE", false); //Men kan youtube url invoeren bij product
  Config::set("PRODUCT_SPOTLIGHT", false); //Men kan aangeven of product op homepage in de spotlight moet komen staan.
  Config::set("PRODUCT_WEIGHT", true); //product heeft eigenschap gewicht
  Config::set('CATALOG_PRODUCT_OPTIONS', 2); //producten kunnen opties hebben. bijv kleuren bij t-shirt
  Config::set('PRODUCT_OPTIONS', ["lbh"]); //product options. Mbv product_option tabel. Benoem properties tbv product excel export
  Config::set("PRODUCT_USE_SUPPLIER", false); //Aankunnen geven van leverancier
  Config::set("PRODUCT_DESCRIPTION_EXPORT", false); //Extra product omschrijving voor export naar bijv. hovenierwinkel, om zo duplicate content te voorkomen.
  Config::set("PRODUCT_ORGAN_PRICE", true); //klantspecifieke  prijs mogelijk
  Config::set("PRODUCT_DISCOUNTGROUP", true); //true/false: maakt gebruik van discountgroepen
  Config::set('PRODUCT_USE_STAFFEL', true); //17-12-2015 - ROBERT - product staffel prijzen mogelijk
  Config::set("PRODUCT_REGISTER_CHANGES", false); //07-01-2016 - ROBERT - registreer wijzingen aan product in product_change
  Config::set('CATALOG_PRODUCT_RELATEDPRODUCTS', true); //product heeft gerelateerde producten
  Config::set('CATALOG_PRODUCT_RELATEDPRODUCTS_DUPLEX', true); //productrelatie wordt in twee richtingen gebruikt
  Config::set('CATALOG_PRODUCT_LINKED_PRODUCTS', true); // product heeft gekoppelde producten
  Config::set('PRODUCT_DISCOUNT', false); //Het is mogelijk een kortingsprijs in te voeren.
  Config::set('PRODUCT_SHOW_INSERTBY', false); //17-10-2016 - ROBERT - Toon product aangemaakt door
  Config::set('PRODUCT_DISCOUNT_PRICE', true); //13-03-2018 - ROBERT = Toon product aanbieding in beheer bij product bewerken. Is deze niet gedefinieerd dan staat is dit zichtbaar.
  Config::set('PRODUCT_SUPPLIER_CODE', true); //28-05-2018 - ROBERT = Toon product leveranciers code. Niet aanwezig, dan niet zichtbaar
  Config::set("PRICE_PRODUCT_DECIMALS", 4); //aantal decimalen bij bewerken factuur. Zet op 4 wanneer er ronde prijzen dienen te komen.
  Config::set('PRODUCT_DISCOUNT_PRICE', false); //product edit discount prijs tonen
  Config::set('PRODUCT_CUSTOM_URL', true); //31-01-2019 - ROBERT - mogelijkheid om een zelf de product url in te voeren
  Config::set('PRODUCT_CONTAINER_CUSTOM_URL', true); // 19-04-2019 - ROB - mogelijkheid om een zelf de product container url in te voeren
  Config::set("PRODUCT_ONLINE_CATEGORY_ONLINE_LINKED", true); //28-02-2019 - ROBERT - het aanpassen van product.online_custshop zal worden doorgezet naar category_product.online.
  // Deze functionaliteit staat standaard aan, echter in sommige gevallen wil je dit niet wanneer je met meerdere online vlaggen werkt, zoals online_uc of online_admin. Je wil de category_product dan online houden, bij het offline zetten van een product. (tot dusver staat dit uit bij landoll/rde)
  Config::set("PRODUCT_USE_ONLINE_UC", true); //07-03-2019 - ROBERT - gebruik van online_uc vlag
  Config::set("PRODUCT_USE_ONLINE_ADMIN", true); //07-03-2019 - ROBERT - gebruik van online_admin vlag
  Config::set('PRODUCT_STOCK_CHANGE_LOG', true); //27-08-2019 - ROBERT - log alle product->stock wijzigingen
  Config::set('PRODUCT_FILES_MULTIPLE', true); //28-11-2019 - ROBERT - meerdere (pdf) bestanden kunnen uploaden bij een product
  Config::set('PRODUCT_USE_SELL_UNIT_OF_MEASUREMENT', true); //06-07-2020 - PAUL - wel/niet de mogelijkheid om unit_of_measurement in te voeren
  Config::set('PRODUCT_IMAGES_MAX_COUNT', 3); //16-02-2022 - ROBERT - nr of images uploadable at a product. main image excluded. default 3.
  Config::set('PRODUCT_USE_PRODUCTCODE', true); //14-03-2022 - ROBERT - multiple ean codes per product (use ProductCode)
  Config::set('PRODUCT_USE_MOST_SOLD', false); //10-05-2022 - PAUL - Enable/disable most sold label (boolean) on product

  //-----------winkelmandje/basket-----------
  Config::set("buy_discount_codes", [
    'A' => 0,
    'B' => 20,
    'C' => 45,
    'D' => 48,
    'E' => 49.6,
  ]); //Inkoop kortingscodes
  Config::set("shipping_categories", [
    'nl' => ['A' => 7.40, 'B' => 28.88],
    'be' => ['A' => 11.53, 'B' => 37.15],
    'de' => ['A' => 11.53, 'B' => 500.00],
    'fr' => ['A' => 18.14, 'B' => 500.00],
  ]); //Verzendkosten. Per land verschillend.
  const CALCULATE_SHIPPING_CAT = false; //Verzendkosten codes, bijv: A = €5, B = €7,50, etc.
  Config::set('BASKET_PICKUP_ENABLED', false); //Maak afhalen beschikbaar
  
  const SHIPPING_COSTS_PARTICULIER = 0.00; //verzendkosten particulier
  Config::set('BASKET_SHIPPING_METHODS', false); //verzend types
  const PAY_ONLINE_DEFAULT_ON = true; //pay_online default 1
  const PAYMENT_OVERMAKEN_PART_ENABLED = true; //overmaken aan voor particulier
  Config::set('SHIPPING_FREE_AMOUNT', [
    'nl' => ['A' => 50.00, 'B' => 500.00],
    'be' => ['A' => 100.00, 'B' => 500.00],
    'de' => ['A' => 150.00, 'B' => false],
    'fr' => ['A' => 200.00, 'B' => false],
  ]); //Geen verzendkosten vanaf
  Config::set('BASKET_ORDER_NO_ACCOUNT', true); //klant kan bestellen zonder account aan te maken
  Config::set('BASKET_DISCOUNTCODE', true); //18-05-2016 - ROBERT - mag kortingscode invoeren
  Config::set("BASKET_IMPORT_EXCEL", true); //07-01-2016 - ROBERT - toon knop importeren excel bij winkelmandje
  Config::set('SHOW_PRODUCT_IMAGE_IN_BASKET', true); //toon de eerste product beeld in het winkelmandje
  Config::set('GA_E-COM_TRACKING', true); //14-06-2016 - ROBERT - Goolge analytcis E-commerce tracking enabled in basket finished
  Config::set("BASKET_ORDER_BACKORDER_ENABLED", true); //12-07-2016 - KOEN - order wordt opgesplitst in te leveren order en back order.
  Config::set("BASKET_ORDER_PAYBEFORE_PERIOD", 14); //27-03-2017 - ROBERT - order moet betaald worden binnnen 14 dagen, anders vervalt deze
  Config::set("BASKET_SHOW_PAYUPFRONT_MSG", true); //27-03-2017 - ROBERT - wanneer klant vooraf moet betalen, laat dan melding zien in winkelmandje
  Config::set("BASKET_ASK_CAPTCHA", [
    "public_key" => "?",
    "secret_key" => "?",
  ]); //23-06-2017 - ROBERT - vraag captcha bij 1e keer product in mandje plaatsen.

  //-----------voorraad/warehousing-----------
  const STOCK_ENABLED = false; //Gebruikt voorraad syteem + controles.
  Config::set("STOCK_ACTIVE", true); //25-06-2018 - ROBERT - kan per product aangeven of deze meedoet aan voorraad beheer
  Config::set("STOCK_LOCATION_MULTI", true); //meerdere voorraad locaties mogelijk
  Config::set("STOCK_LOCATION", true); //gebruik vaklocatie bij voorraad
  Config::set("STOCK_LEVEL", true); //30-05-2018 - ROBERT - toon Product stock_level en stock_level_max
  Config::set("STOCK_PACKINGSIZE", true); //30-05-2018 - ROBERT - toon Product packing_size
  Config::set("STOCK_NOT_IN_BACKORDER", true); //30-05-2018 - ROBERT - toon Product not_in_backorder
  Config::set("STOCK_FLOW_USE_SUPPLIER", true); //13-06-2018 - ROBERT - gebruik leverancier
  Config::set("STOCK_FLOW_USE_BRAND", true); //13-06-2018 - ROBERT - gebruik merk


  //-----------GSDFW PLUGINS-----------
  const DIR_PLUGIN_FOLDER = DIR_ROOT_GSDFW . 'plugins/'; //locatie van GSDFW plugin folder
  const URL_PLUGIN_FOLDER = '/gsdfw/plugins/'; //url van GSDFW plugin folder
  Config::set("GSDFW_PLUGINS", ['timeregistration']);  //geactiveerd plugins voor dit project

  //-----------WEBP--------------------
  Config::set("IMAGES_WEBP_CONVERT", true); //08-11-2022 - PAUL - Afbeeldingen (producten/catalogs/pagina teaser/pagina afbeeldingen/gallery afbeeldingen altijd omzetten naar webp formaat
  Config::set("IMAGES_WEBP_CONVERT_QUALITY", 80); //08-11-2022 - PAUL - Kwaliteit van de webp afbeeldingen

  //-----------CONTACT-----------------
  Config::set("SEND_RECEIVED_MAIL_CONTACTFORM", false); //Send a confirmation email to the customer, to thank him/her for filling in the form.

  //-----------QUOTATION-MODULE--------
  Config::set('QUOTATION_PREFIX', false); // Define string to place in front of the quotation number e.g. 'QU_' will give a quotationnumber of 'QU_20220001';

	Config::set('USE_PRODUCTIMAGE_TITLES', false); // 30-09-2022 - PAUL - Wel/niet mogelijkheid om product afbeelding titels te kunnen zetten in beheer.


  Config::set('MODULE_MAPPING', [
    1 => [
      "active" > DEVELOPMENT, //is deze mapping actief
      "site" => "siteschoorsteenplaat", //map deze module van naar.
    ],
    7 => [
      "active" > DEVELOPMENT, //is deze mapping actief
      "site" => "siteheblad", //map deze module van naar.
    ],
  ]); //14-09-2023 - ROBERT - met deze config kun je een bepaalde Site.id, een module mappen naar een andere module. Bijvoorbeeld om een aparte site module te maken per website.

  Config::set("PRIMARY_SITES_ONLY", []); // usergroups that may only view primary sites.

  Config::set('SITE_MESSAGE_TYPES', [
    "popupbasket" => "Popup winkelmandje",
    "topmessage"  => "Bericht boven header",
    "popup"       => "Popup bericht",
    "popupimage"  => "Popup afbeelding",
  ]); // 17-07-2025 - JUSTIN - met deze config kun je de types van site messages overschrijven. Bijvoorbeeld als je voor een project een specifiekere bericht type nodig hebt.