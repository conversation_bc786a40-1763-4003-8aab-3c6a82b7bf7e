-- 29-08-2017 - ROB - nieuwe faq module

-- e<PERSON>t oude faq tabel verwijderen (wordt nergens gebru<PERSON>t)
DROP TABLE IF EXISTS faq;

-- nieuwe tabellen voor de faq module
CREATE TABLE `faq` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `active` tinyint(1) DEFAULT '1',
  `sort` SMALLINT(5) UNSIGNED NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `faq_category` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `sort` SMALLINT(5) UNSIGNED NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `i_faqcategory_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `faq_cat` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `faq_category_id` mediumint(8) UNSIGNED NOT NULL,
  `faq_id` mediumint(8) UNSIGNED NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fk_faqcat_faq` (`faq_id`),
  KEY `fk_faqcat_faqcat` (`faq_category_id`),
  CONSTRAINT `fk_faqcat_faq` FOREIGN KEY (`faq_id`) REFERENCES `faq` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_faqcat_faqcat` FOREIGN KEY (`faq_category_id`) REFERENCES `faq_category` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `faq_content` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `faq_id` mediumint(8) UNSIGNED NOT NULL,
  `locale` varchar(2) NOT NULL DEFAULT 'nl',
  `question` varchar(255) DEFAULT NULL,
  `answer` text,
  PRIMARY KEY (`id`),
  KEY `faq_id` (`faq_id`),
  KEY `locale` (`locale`),
  CONSTRAINT `faq_content_ibfk_1` FOREIGN KEY (`faq_id`) REFERENCES `faq` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- -------------------------- VDLCONTAINER - STAGING ----------------------------------- RELEASED: 05-03-2021 VERSIE 6.7.5 [PLUGINS: faq] [RUN WEBPACK BACKEND ON RELEASE]