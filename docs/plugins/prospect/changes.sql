-- Prospect module --
CREATE TABLE IF NOT EXISTS `prospect` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `owner_user_id` mediumint(8) UNSIGNED NOT NULL,
  `owner_organisation_id` mediumint(8) UNSIGNED NOT NULL,
  `company_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `type` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `address` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `number` varchar(45) COLLATE utf8_unicode_ci DEFAULT NULL,
  `zip` varchar(12) COLLATE utf8_unicode_ci DEFAULT NULL,
  `city` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` char(2) COLLATE utf8_unicode_ci DEFAULT 'nl',
  `language` char(2) COLLATE utf8_unicode_ci DEFAULT 'nl',
  `email` varchar(150) COLLATE utf8_unicode_ci DEFAULT NULL,
  `phone` varchar(25) COLLATE utf8_unicode_ci DEFAULT NULL,
  `website` varchar(150) COLLATE utf8_unicode_ci DEFAULT NULL,
  `intern_remark` text COLLATE utf8_unicode_ci,
  `lat` float DEFAULT NULL,
  `lng` float DEFAULT NULL,
  `user_sex` char(1) CHARACTER SET utf8 DEFAULT NULL,
  `user_initials` varchar(20) CHARACTER SET utf8 DEFAULT NULL,
  `user_firstname` varchar(50) CHARACTER SET utf8 DEFAULT NULL,
  `user_insertion` varchar(20) CHARACTER SET utf8 DEFAULT NULL,
  `user_lastname` varchar(100) CHARACTER SET utf8 DEFAULT NULL,
  `user_phone` varchar(25) CHARACTER SET utf8 DEFAULT NULL,
  `user_cellphone` varchar(25) CHARACTER SET utf8 DEFAULT NULL,
  `user_email` varchar(150) CHARACTER SET utf8 DEFAULT NULL,
  `category` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `approach_date` date DEFAULT NULL,
  `no_approach` tinyint(1) UNSIGNED DEFAULT '0',
  `filter1` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `filter2` VARCHAR(25) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `void` tinyint(1) NOT NULL DEFAULT '0',
  `insertTS` datetime NOT NULL,
  `updateTS` datetime NOT NULL,
  `insertUser` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `updateUser` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE IF NOT EXISTS `prospect_communication` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `prospect_id` mediumint(8) UNSIGNED NOT NULL,
  `subject` varchar(255) DEFAULT NULL,
  `type` varchar(100) DEFAULT NULL,
  `date` date DEFAULT NULL,
  `remark` text,
  PRIMARY KEY (`id`),
  KEY `prospect_id` (`prospect_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

-- ---------------------------STAGING LAVRI -------------------------------------------- RELEASED: 15-11-2017 VERSIE 4.6.0 [PLUGINS: prospect]
-- ---------------------------LIVE LAVRI ----------------------------------------------- RELEASED: 15-11-2017 VERSIE 4.6.0 [PLUGINS: prospect]