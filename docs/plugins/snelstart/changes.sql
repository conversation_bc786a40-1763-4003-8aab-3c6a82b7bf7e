-- 09-11-2020 - ROBERT - snelstart table
CREATE TABLE IF NOT EXISTS `snelstart` (
    `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
    `organisation_id` mediumint(8) UNSIGNED NOT NULL,
    `client_key` text DEFAULT NULL,
    `access_token` text CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
    `access_token_datetime` datetime DEFAULT NULL,
    `token_type` varchar(10) DEFAULT NULL,
    `expires_in` smallint(5) UNSIGNED DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `snelstart_organ_id` (`organisation_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- -------------------------- VDHOUTMETAAL - LIVE -------------------------------------- RELEASED: 11-11-2020 VERSIE 6.6.5

