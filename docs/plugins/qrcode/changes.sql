-- 26-10-2017 - ROBERT - <PERSON>IT qrcodes from ronald jeans
CREATE TABLE IF NOT EXISTS `ip_group_country` (
  `ip_start` bigint(20) NOT NULL,
  `ip_cidr` varchar(20) NOT NULL,
  `country_code` varchar(2) NOT NULL,
  `country_name` varchar(64) NOT NULL,
  UNIQUE KEY `ip_start` (`ip_start`),
  KEY `country` (`country_code`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `qrproduct` (
  `id` mediumint(8) NOT NULL AUTO_INCREMENT,
  `organisation_id` mediumint(8) DEFAULT NULL,
  `user_id` varchar(20) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `url` varchar(255) NOT NULL,
  `type` tinyint(3) UNSIGNED DEFAULT NULL,
  `deleted` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `name` (`name`),
  <PERSON>EY `url` (`url`),
  <PERSON><PERSON><PERSON> `organisation_id` (`organisation_id`)
) ENGINE=MyISAM AUTO_INCREMENT=180 DEFAULT CHARSET=latin1;

CREATE TABLE IF NOT EXISTS `qrstat` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `qrproduct_id` mediumint(8) NOT NULL,
  `country` varchar(255) NOT NULL,
  `ip` varchar(20) NOT NULL,
  `insertTS` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `qrproduct_id` (`qrproduct_id`),
  KEY `country` (`country`),
  KEY `insertTS` (`insertTS`)
) ENGINE=MyISAM AUTO_INCREMENT=2734 DEFAULT CHARSET=latin1;
COMMIT;

ALTER TABLE `qrstat` CHANGE `country` `country` VARCHAR(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL DEFAULT NULL;
ALTER TABLE `qrstat` CHANGE `ip` `ip` VARCHAR(40) CHARACTER SET latin1 COLLATE latin1_swedish_ci NULL;

-- -------------------------- LIVE RJEANS ---------------------------------------------- RELEASED: 16-04-2018 VERSIE 4.8.3

