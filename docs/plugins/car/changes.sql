
CREATE TABLE IF NOT EXISTS `car` (
  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` ENUM('new', 'used') NULL,
  `brand` VARCHAR(45) NULL,
  `code` SMALLINT(100) NULL,
  `constructionyear` YEAR NULL,
  `drivenkilometers` MEDIUMINT NULL,
  `fueltype` VARCHAR(20) NULL,
  `transmission` ENUM('manual', 'auto', 'semiauto') NULL,
  `price` VARCHAR(100) NULL,
  `online` TINYINT(1) NOT NULL DEFAULT '1',
  `spotlight` TINYINT(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  INDEX `i_car_type` (`type` ASC),
  INDEX `i_car_brand` (`brand` ASC),
  INDEX `i_car_code` (`code` ASC),
  INDEX `i_car_constryear` (`constructionyear` ASC),
  INDEX `i_car_fueltype` (`fueltype` ASC),
  INDEX `i_car_transm` (`transmission` ASC),
  INDEX `i_car_online` (`online` ASC),
  INDEX `i_car_spotlight` (`spotlight` ASC),
  INDEX `i_car_price` (`price` ASC))
  ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `car_content` (
  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `car_id` MEDIUMINT(8) UNSIGNED NOT NULL,
  `locale` CHAR(2) NULL DEFAULT 'nl',
  `title` VARCHAR(255) NULL,
  `description` TEXT NULL,
  PRIMARY KEY (`id`),
  INDEX `i_carcontent_title` (`title` ASC),
  INDEX `i_carcontent_locale` (`locale` ASC),
  INDEX `fk_car_content_car_idx` (`car_id` ASC),
  CONSTRAINT `fk_car_content_car`
  FOREIGN KEY (`car_id`)
  REFERENCES `car` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE)
  ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS `car_image` (
  `id` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `car_id` MEDIUMINT(8) UNSIGNED NOT NULL,
  `filename_thumb` VARCHAR(255) NULL,
  `filename_large` VARCHAR(255) NULL,
  `sort` SMALLINT(5) UNSIGNED NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_car_image_car_idx` (`car_id` ASC),
  CONSTRAINT `fk_car_image_car`
  FOREIGN KEY (`car_id`)
  REFERENCES `car` (`id`)
    ON DELETE CASCADE
    ON UPDATE CASCADE)
  ENGINE = InnoDB;