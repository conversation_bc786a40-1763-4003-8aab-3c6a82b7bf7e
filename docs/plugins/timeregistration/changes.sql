-- 26-10-2017 - ROBERT - INIT timeregistration from heblad
CREATE TABLE `worker` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `group` mediumint(8) UNSIGNED DEFAULT NULL,
  `inpaymentsince` date DEFAULT NULL,
  `outpaymentdate` date DEFAULT NULL,
  `hours_workweek` decimal(8,2) DEFAULT NULL,
  `hour_for_hour` tinyint(1) DEFAULT NULL,
  `firstname` varchar(150) DEFAULT NULL,
  `prefix` varchar(45) DEFAULT NULL,
  `lastname` varchar(150) NOT NULL,
  `street` varchar(255) DEFAULT NULL,
  `number` varchar(10) DEFAULT NULL,
  `zip` varchar(10) DEFAULT NULL,
  `city` varchar(45) DEFAULT NULL,
  `country` varchar(2) DEFAULT NULL,
  `birthday` date DEFAULT NULL,
  `ischeckedin` tinyint(3) UNSIGNED DEFAULT NULL,
  `photofilename` varchar(255) DEFAULT NULL,
  `cellphone` varchar(15) DEFAULT NULL,
  `phone` varchar(15) DEFAULT NULL,
  `remark` text,
  `bankaccount` varchar(40) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `persnumber` varchar(12) DEFAULT NULL,
  `sort` tinyint(3) UNSIGNED DEFAULT NULL,
  `disabled` tinyint(4) DEFAULT '0',
  `stenen` enum('Y','N') NOT NULL DEFAULT 'N',
  `loodsnijden` enum('Y','N') NOT NULL DEFAULT 'N',
  `opstortje` enum('Y','N') NOT NULL DEFAULT 'N',
  `onderplaten` enum('Y','N') NOT NULL DEFAULT 'N',
  `bovenplaten` enum('Y','N') NOT NULL DEFAULT 'N',
  `rollaag` enum('Y','N') NOT NULL DEFAULT 'N',
  `pijpen` enum('Y','N') NOT NULL DEFAULT 'N',
  `metselen` enum('Y','N') NOT NULL DEFAULT 'N',
  `plasticol` enum('Y','N') NOT NULL DEFAULT 'N',
  `inwassen` enum('Y','N') NOT NULL DEFAULT 'N',
  `voegen` enum('Y','N') NOT NULL DEFAULT 'N',
  `extralood` enum('Y','N') NOT NULL DEFAULT 'N',
  `voorraad` enum('Y','N') NOT NULL DEFAULT 'N',
  `prikklok` tinyint(1) NOT NULL DEFAULT '0',
  `insertTS` datetime NOT NULL,
  `insertUser` mediumint(8) UNSIGNED DEFAULT NULL,
  `updateTS` datetime NOT NULL,
  `updateUser` mediumint(8) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `worker_assets` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `worker_id` mediumint(8) UNSIGNED NOT NULL,
  `filename_orig` varchar(255) DEFAULT NULL,
  `filename` varchar(255) DEFAULT NULL,
  `variant` varchar(20) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL,
  `type` smallint(6) DEFAULT NULL,
  `contracttype` varchar(10) DEFAULT NULL,
  `name` varchar(100) DEFAULT NULL,
  `description` text,
  `valid_until` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `worker_baseleave` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `date` date DEFAULT NULL,
  `description` text,
  `type` enum('snipper','paid_off') DEFAULT NULL,
  `insertTS` datetime NOT NULL,
  `insertUser` mediumint(8) UNSIGNED DEFAULT NULL,
  `updateTS` datetime NOT NULL,
  `updateUser` mediumint(8) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `worker_function` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `worker_id` mediumint(8) UNSIGNED NOT NULL,
  `function` varchar(25) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `worker_group` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `worker_screen_id` mediumint(8) UNSIGNED DEFAULT NULL,
  `worker_screen_id2` mediumint(8) UNSIGNED DEFAULT NULL,
  `sendemail` tinyint(4) DEFAULT '0',
  `email` varchar(255) DEFAULT NULL,
  `on_loan_list` tinyint(1) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `worker_hours` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `worker_id` mediumint(8) UNSIGNED NOT NULL,
  `in` datetime DEFAULT NULL,
  `out` datetime DEFAULT NULL,
  `minutes` mediumint(4) DEFAULT NULL,
  `pauze` mediumint(4) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE `worker_screen` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `name` varchar(45) DEFAULT NULL,
  `ip1` varchar(45) DEFAULT NULL,
  `ip2` varchar(45) DEFAULT NULL,
  `ip3` varchar(45) DEFAULT NULL,
  `ip4` varchar(45) DEFAULT NULL,
  `ip5` varchar(45) DEFAULT NULL,
  `ip6` varchar(45) DEFAULT NULL,
  `ip7` varchar(45) DEFAULT NULL,
  `ip8` varchar(45) DEFAULT NULL,
  `ip9` varchar(45) DEFAULT NULL,
  `ip10` varchar(45) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `worker_sheet` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `worker_id` mediumint(8) UNSIGNED DEFAULT NULL,
  `from` datetime DEFAULT NULL,
  `to` datetime DEFAULT NULL,
  `description` text,
  `type` enum('snipper','paid_off','atv','ill','unpaid_off') DEFAULT NULL,
  `hours` decimal(8,2) DEFAULT NULL,
  `insertTS` datetime NOT NULL,
  `insertUser` mediumint(8) UNSIGNED DEFAULT NULL,
  `updateTS` datetime NOT NULL,
  `updateUser` mediumint(8) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `worker_workdays` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `worker_id` mediumint(8) UNSIGNED DEFAULT NULL,
  `dow` tinyint(8) UNSIGNED DEFAULT NULL,
  `hours` decimal(8,2) UNSIGNED DEFAULT NULL,
  `in` decimal(8,2) UNSIGNED DEFAULT NULL,
  `out` decimal(8,2) UNSIGNED DEFAULT NULL,
  `pauze` decimal(8,2) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `worker_yearly_credit` (
  `id` mediumint(8) UNSIGNED NOT NULL,
  `worker_id` mediumint(8) UNSIGNED NOT NULL,
  `year` varchar(4) DEFAULT NULL,
  `month` tinyint(2) UNSIGNED DEFAULT NULL,
  `snipper` decimal(8,2) DEFAULT NULL,
  `atv` decimal(8,2) DEFAULT NULL,
  `overtime` mediumint(8) DEFAULT NULL,
  `startsaldo` decimal(8,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `worker`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sort` (`sort`),
  ADD KEY `group` (`group`),
  ADD KEY `ischeckedin` (`ischeckedin`),
  ADD KEY `stenen` (`stenen`),
  ADD KEY `loodsnijden` (`loodsnijden`),
  ADD KEY `opstortje` (`opstortje`),
  ADD KEY `onderplaten` (`onderplaten`),
  ADD KEY `bovenplaten` (`bovenplaten`),
  ADD KEY `rollaag` (`rollaag`),
  ADD KEY `pijpen` (`pijpen`),
  ADD KEY `metselen` (`metselen`),
  ADD KEY `plasticol` (`plasticol`),
  ADD KEY `inwassen` (`inwassen`),
  ADD KEY `voegen` (`voegen`),
  ADD KEY `extralood` (`extralood`);

ALTER TABLE `worker_assets`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_worker_as` (`worker_id`);

ALTER TABLE `worker_baseleave`
  ADD PRIMARY KEY (`id`),
  ADD KEY `date` (`date`);

ALTER TABLE `worker_function`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_wf_worker_id_idx` (`worker_id`),
  ADD KEY `i_wf_function` (`function`);

ALTER TABLE `worker_group`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_wg_id` (`id`),
  ADD KEY `fk_wks_id` (`worker_screen_id`),
  ADD KEY `fk_wks_id2` (`worker_screen_id2`);

ALTER TABLE `worker_hours`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_wh_id` (`worker_id`),
  ADD KEY `in` (`in`),
  ADD KEY `out` (`out`);

ALTER TABLE `worker_screen`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `worker_sheet`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_worker_id` (`worker_id`);

ALTER TABLE `worker_workdays`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_worker_workdays` (`worker_id`);

ALTER TABLE `worker_yearly_credit`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_snip_worker_id` (`worker_id`);

ALTER TABLE `worker`
  MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=95;

ALTER TABLE `worker_assets`
  MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=390;

ALTER TABLE `worker_baseleave`
  MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

ALTER TABLE `worker_function`
  MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

ALTER TABLE `worker_group`
  MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

ALTER TABLE `worker_hours`
  MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=71712;

ALTER TABLE `worker_screen`
  MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

ALTER TABLE `worker_sheet`
  MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2682;

ALTER TABLE `worker_workdays`
  MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=605;

ALTER TABLE `worker_yearly_credit`
  MODIFY `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=85;

ALTER TABLE `worker`
  ADD CONSTRAINT `worker_ibfk_1` FOREIGN KEY (`group`) REFERENCES `worker_group` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;


ALTER TABLE `worker_assets`
  ADD CONSTRAINT `fk_worker_as` FOREIGN KEY (`worker_id`) REFERENCES `worker` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `worker_function`
  ADD CONSTRAINT `fk_wf_worker_id` FOREIGN KEY (`worker_id`) REFERENCES `worker` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `worker_group`
  ADD CONSTRAINT `fk_wks_id` FOREIGN KEY (`worker_screen_id`) REFERENCES `worker_screen` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_wks_id2` FOREIGN KEY (`worker_screen_id2`) REFERENCES `worker_screen` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `worker_group_ibfk_1` FOREIGN KEY (`worker_screen_id`) REFERENCES `worker_screen` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `worker_group_ibfk_2` FOREIGN KEY (`worker_screen_id`) REFERENCES `worker_screen` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE `worker_hours`
  ADD CONSTRAINT `worker_hours_ibfk_1` FOREIGN KEY (`worker_id`) REFERENCES `worker` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `worker_sheet`
  ADD CONSTRAINT `fk_worker_id` FOREIGN KEY (`worker_id`) REFERENCES `worker` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `worker_workdays`
  ADD CONSTRAINT `fk_worker_workdays` FOREIGN KEY (`worker_id`) REFERENCES `worker` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;


ALTER TABLE `worker_yearly_credit`
  ADD CONSTRAINT `worker_yearly_credit_ibfk_1` FOREIGN KEY (`worker_id`) REFERENCES `worker` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 26-10-2017 - ROBERT - external id

ALTER TABLE `worker` ADD `external_id` VARCHAR(30) CHARACTER SET utf8 COLLATE utf8_bin NULL AFTER `group`;

-- 09-11-2017 - ROBERT - opruimen functions
ALTER TABLE `worker`
  DROP `stenen`,
  DROP `loodsnijden`,
  DROP `opstortje`,
  DROP `onderplaten`,
  DROP `bovenplaten`,
  DROP `rollaag`,
  DROP `pijpen`,
  DROP `metselen`,
  DROP `plasticol`,
  DROP `inwassen`,
  DROP `voegen`,
  DROP `extralood`,
  DROP `voorraad`;

-- 07-12-2017 - ROBERT - bijhouden
ALTER TABLE worker_assets
  ADD `insertTS` datetime DEFAULT NULL,
  ADD `insertUser` mediumint(8) UNSIGNED DEFAULT NULL;

ALTER TABLE `worker` CHANGE `ischeckedin` `ischeckedin` BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE `worker` CHANGE `disabled` `disabled` BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE `worker` ADD `planning` BOOLEAN NOT NULL DEFAULT FALSE AFTER prikklok;
ALTER TABLE `worker` CHANGE `hour_for_hour` `hour_for_hour` BOOLEAN NOT NULL DEFAULT FALSE;

CREATE TABLE IF NOT EXISTS `worker_profile` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `worker_id` mediumint(8) UNSIGNED NOT NULL,
  `type` varchar(45) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `code` varchar(45) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
  `value` text CHARACTER SET utf8 COLLATE utf8_bin,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`worker_id`,`code`),
  KEY `workerid` (`worker_id`),
  KEY `code` (`code`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
ALTER TABLE `worker_profile`  ADD CONSTRAINT `worker_profile_ibfk_1` FOREIGN KEY (`worker_id`) REFERENCES `worker` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE `worker_profile` CHANGE `value` `value` VARCHAR(255) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL;
ALTER TABLE `worker_profile` ADD INDEX(`value`);

-- 11-05-2018 - ROB - Projecten voor urenregistratie
CREATE TABLE IF NOT EXISTS `worker_project` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `company_organ_id` mediumint(8) UNSIGNED NOT NULL,
  `title` VARCHAR(255) NOT NULL,
  `description` TEXT DEFAULT NULL,
  `status` VARCHAR(30) DEFAULT 'new',
  `invoiceable` tinyint(1) UNSIGNED DEFAULT 0,
  `hourly_rate` DECIMAL(10,2) DEFAULT NULL,
  `project_rate` DECIMAL(10,2) DEFAULT NULL,
  `hourly_budget` DECIMAL(10,2) DEFAULT NULL,
  `visible_to_client` tinyint(1) UNSIGNED DEFAULT 0,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 11-05-2018 - ROB - Uren invullen op basis van projecten
CREATE TABLE `worker_project_hours` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `worker_id` mediumint(8) UNSIGNED NOT NULL,
  `date` DATE NOT NULL,
  `minutes` mediumint(4) NOT NULL,
  `worker_project_id` mediumint(8) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `invoice_product_id` mediumint(8) DEFAULT NULL,
  `status` varchar(30) NOT NULL DEFAULT 'new',
  `insertTS` datetime NOT NULL,
  `insertUser` mediumint(8) UNSIGNED DEFAULT NULL,
  `updateTS` datetime NOT NULL,
  `updateUser` mediumint(8) UNSIGNED DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- 06-12-2018 - ROBERT - flexible types
ALTER TABLE `worker_baseleave` CHANGE `type` `type` VARCHAR(30) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL;
ALTER TABLE `worker_sheet` CHANGE `type` `type` VARCHAR(30) CHARACTER SET utf8 COLLATE utf8_bin NULL DEFAULT NULL;


-- 19-06-2019 - ROBERT - meenemen in berekening voorgaand jaar
ALTER TABLE `worker_baseleave` ADD `calc_prev_year` BOOLEAN NOT NULL DEFAULT FALSE AFTER `type`;

-- 20-06-2019 - ROBERT - rde standaard urenlijsten maken voor w&f
CREATE TABLE `worker_cust_week` (
   `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
   `worker_id` mediumint(8) UNSIGNED NOT NULL,
   `year` mediumint(4) NOT NULL,
   `week` mediumint(4) NOT NULL,
   PRIMARY KEY (`id`),
   INDEX(`worker_id`),
   INDEX(`year`),
   INDEX(`week`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE `worker_cust_day` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `worker_cust_week_id` mediumint(8) UNSIGNED NOT NULL,
  `worker_id` mediumint(8) UNSIGNED NOT NULL,
  `date` DATE NOT NULL,
  `work_minutes` mediumint(4) DEFAULT NULL,
  `paidleave_minutes` mediumint(4) DEFAULT NULL,
  `unpaidleave_minutes` mediumint(4) DEFAULT NULL,
  `overtime1_minutes` mediumint(4) DEFAULT NULL,
  `overtime2_minutes` mediumint(4) DEFAULT NULL,
  PRIMARY KEY (`id`),
  INDEX(`worker_id`),
  INDEX(`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
ALTER TABLE `worker_cust_day` ADD FOREIGN KEY (`worker_cust_week_id`) REFERENCES `worker_cust_week`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- 29-04-2020 - ROBERT - Groeperen van rijen op factuur
ALTER TABLE `worker_project` ADD `invoice_group_rows` BOOLEAN NOT NULL DEFAULT FALSE AFTER `visible_to_client`;

-- 29-04-2020 - ROBERT - Grotere description
ALTER TABLE `worker_project_hours` CHANGE `description` `description` TEXT CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL;

-- 02-11-2020 - ROB - prikklok rechten voor werknemer
ALTER TABLE `worker` ADD `may_complete_project` tinyint(1) UNSIGNED NOT NULL DEFAULT  '0' AFTER `planning`;

-- 17-11-2020 - ROBERT - het verwijderen van een baseleave verwijderd ook de workerhours die zijn aangemaakt bij deze baseleave
ALTER TABLE `worker_sheet` ADD `worker_baseleave_id` MEDIUMINT(8) NULL DEFAULT NULL AFTER `worker_id`;
ALTER TABLE `worker_sheet` ADD INDEX(`worker_baseleave_id`);

-- 23-11-2020 - ROB - Project kunnen toevoegen aan geregistreerde uren
ALTER TABLE `worker_hours` ADD `project_id` mediumint(8) UNSIGNED NULL AFTER `worker_id` ;

-- -------------------------- VDHOUTMETAAL - LIVE -------------------------------------- RELEASED: 09-12-2020 VERSIE 6.7.0
-- -------------------------- LIVE HEBLAD ---------------------------------------------- RELEASED: 16-12-2020 VERSIE 6.7.0 [PLUGINS: timeregistration]
-- ---------------------------LIVE RDE ------------------------------------------------- RELEASED: 17-12-2020 VERSIE 6.7.1 [PLUGINS: timeregistration]
-- -------------------------- GSD - LIVE - --------------------------------------------- RELEASED: 17-12-2020 VERSIE 6.5.1 [PLUGINS: timeregistration]

-- vanaf hier gebruiken we het updater script
