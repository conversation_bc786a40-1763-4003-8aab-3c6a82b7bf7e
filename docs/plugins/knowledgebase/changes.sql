CREATE TABLE IF NOT EXISTS `kb` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `kb_category_id` mediumint(8) NOT NULL,
  `active` tinyint(1) DEFAULT 1,
  `sort` smallint(5) UNSIGNED DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `active` (`active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `kb_category` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `sort` smallint(5) UNSIGNED DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `i_kbcategory_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `kb_content` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `kb_id` mediumint(8) UNSIGNED NOT NULL,
  `locale` varchar(2) NOT NULL DEFAULT 'nl',
  `question` varchar(255) DEFAULT NULL,
  `answer` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `kb_id` (`kb_id`),
  KEY `locale` (`locale`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

ALTER TABLE `kb_content`
  ADD CONSTRAINT `kb_content_ibfk_1` FOREIGN KEY (`kb_id`) REFERENCES `kb` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

CREATE TABLE IF NOT EXISTS `kb_file` (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT,
  `kb_id` mediumint(8) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `filename` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `kb_id` (`kb_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- -------------------------- LIVE HEBLAD ---------------------------------------------- RELEASED: 17-11-2020 VERSIE 6.6.5 [PLUGINS: timeregistration]

