trigger:
  branches:
    include:
      - master
      - feature/*

schedules:
  - cron: 0 22 * * 0
    branches:
      include:
        - master
    always: true

jobs:
- job: Standard

  pool:
    name: Rabo-Windows-Production

  steps:
  - task: CredScan@2
    displayName: 'Run Credential Scanner'
    
  - script: composer install --no-interaction --prefer-dist
    displayName: 'Composer install'

  - script: .\vendor\bin\phpunit --log-junit test-results.xml --whitelist src --coverage-clover coverage-report
    displayName: 'Run tests'

  - task: PublishTestResults@2
    displayName: 'Publish test results'
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: 'test-results.xml'
      failTaskOnFailedTests: true
      testRunTitle: 'Unit tests'

  - task: SonarQubePrepare@4
    displayName: 'Prepare analysis on SonarQube'
    inputs:
      SonarQube: $(sonarQubeServiceConnection)
      scannerMode: CLI
      configMode: manual
      cliProjectKey: 'nl.rabobank.gict.payments_savings.omnikassa_frontend.sdk:php'
      cliProjectName: 'Rabobank OmniKassa - PHP SDK'
      extraProperties: |
        sonar.inclusions=src/**
        sonar.php.tests.reportPath=test-results.xml
        sonar.php.coverage.reportPaths=coverage-report

  - task: SonarQubeAnalyze@4
    displayName: 'Run SonarQube Code Analysis'      

  - task: SonarQubePublish@4
    displayName: 'Publish SonarQube Quality Gate Result'

  - script: |
     echo "##vso[task.setvariable variable=JAVA_HOME]$(JAVA_HOME_11_X64)"
     echo "##vso[task.setvariable variable=PATH]$(JAVA_HOME_11_X64)\bin;$(PATH)"
    displayName: 'Set Java to Version 11'

  - task: NexusIqPipelineTask@1
    displayName: 'Nexus IQ policy evaluation'
    inputs:
      nexusIqService: $(nexusServiceConnection)
      applicationId: 'omnikassa-frontend-sdk-php'
      stage: release
      scanTargets: 'composer.lock'

  - task: PostAnalysis@1
    displayName: 'Post Analysis'
    inputs:
      CredScan: true

- job: Fortify

  pool:
    name: Rabo-Linux-Production

  steps:
  - task: FortifySCA@5
    displayName: 'Run Fortify'
    inputs:
      applicationType: php
      buildSourceVersion: 1.8
      fortifyBuildId: 'ps_omnikassa-sdk-php_fortify'
      runFortifyUpload: true
      fortifyServerName: $(fortifyServiceConnection)
      fortifyApplicationName: 'ps_omnikassa-php-sdk_fortify'
      fortifyApplicationVersion: 1.0
