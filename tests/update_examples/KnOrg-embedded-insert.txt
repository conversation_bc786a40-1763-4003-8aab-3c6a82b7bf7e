; We need to specify DEFAULT_CHANGE | ALLOW_CHANGES | ALLOW_REFORMAT_PHONE_NR
; to get In & SeNm populated, split the house number off the street field and
; reformat the Dutch phone number.
KnOrganisation:insert:1079
[
  'name' => 'Wyz',
  'address' => [
    'street' => 'Govert Flinckstraat 168A',
; My old notes imply that AFAS itself normalizes zipcode to insert a space
; between numbers and letters, and convert letters to uppercase. So this library
; does not (need to) do that. If this is not true, someone please tell me.
    'zip_code' => '1072EP',
    'town' => 'Amsterdam',
; This is an ISO country code, not an AFAS code (though in the case of 'NL'
; these are the same). The ObjectWithCountry class converts it to an AFAS code.
    'country_iso' => 'NL',
  ],
  'contact' => [
    'email' => '<EMAIL>',
    'phone' => '+31622517218',
    'person' => [
      'first_name' => '<PERSON><PERSON><PERSON>',
      'last_name' => '<PERSON><PERSON>',
    ],
  ],
]
--
{
    "KnOrganisation": {
        "Element": {
            "Fields": {
                "Nm": "Wyz",
                "PbAd": true,
                "AutoNum": true,
; MatchOga has a default of 6 (for inserts).
                "MatchOga": 6
            },
            "Objects": {
                "KnBasicAddressAdr": {
                    "Element": {
                        "Fields": {
                            "Ad": "Govert Flinckstraat",
                            "ZpCd": "1072EP",
                            "Rs": "Amsterdam",
                            "CoId": "NL",
                            "HmNr": 168,
                            "HmAd": "A",
                            "PbAd": false,
                            "ResZip": false
                        }
                    }
                },
                "KnContact": {
                    "Element": {
                        "Fields": {
                            "TeNr": "06-22517218",
                            "EmAd": "<EMAIL>",
                            "ViKc": "PRS"
                        },
                        "Objects": {
                            "KnPerson": {
                                "Element": {
                                    "Fields": {
                                        "FiNm": "Roderik",
                                        "LaNm": "Muit",
                                        "In": "R.",
                                        "SeNm": "MUIT",
                                        "SpNm": false,
                                        "ViGe": "O",
                                        "Corr": false,
                                        "AutoNum": true,
; MatchPer has a default of 7 (for inserts).
                                        "MatchPer": 7
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
--
<KnOrganisation xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Element>
    <Fields Action="insert">
      <Nm>Wyz</Nm>
      <PbAd>1</PbAd>
      <AutoNum>1</AutoNum>
      <MatchOga>6</MatchOga>
    </Fields>
    <Objects>
      <KnBasicAddressAdr>
        <Element>
          <Fields Action="insert">
            <Ad>Govert Flinckstraat</Ad>
            <ZpCd>1072EP</ZpCd>
            <Rs>Amsterdam</Rs>
            <CoId>NL</CoId>
            <HmNr>168</HmNr>
            <HmAd>A</HmAd>
            <PbAd>0</PbAd>
            <ResZip>0</ResZip>
          </Fields>
        </Element>
      </KnBasicAddressAdr>
      <KnContact>
        <Element>
          <Fields Action="insert">
            <TeNr>06-22517218</TeNr>
            <EmAd><EMAIL></EmAd>
            <ViKc>PRS</ViKc>
          </Fields>
          <Objects>
            <KnPerson>
              <Element>
                <Fields Action="insert">
                  <FiNm>Roderik</FiNm>
                  <LaNm>Muit</LaNm>
                  <In>R.</In>
                  <SeNm>MUIT</SeNm>
                  <SpNm>0</SpNm>
                  <ViGe>O</ViGe>
                  <Corr>0</Corr>
                  <AutoNum>1</AutoNum>
                  <MatchPer>7</MatchPer>
                </Fields>
              </Element>
            </KnPerson>
          </Objects>
        </Element>
      </KnContact>
    </Objects>
  </Element>
</KnOrganisation>
