; Integer fields (Unit & quantity) will be converted to integer in the JSON
; but are accepted as both integer and (numeric) string. 'Unit' starts with
; uppercase here because it's the actual AFAS field name; the others are
; aliases. (We use aliases because most AFAS names are harder to read.)
FbSales:insert
[
  'reference' => 'Weborder 123',
  'debtor_id' => 25000,
  'currency_code' => 'EUR',
  'warehouse' => '*** - ******',
  'Unit' => '1',
  'line_items' => [
    [
      'item_code' => 'xxxxx',
      'quantity' => '5',
      'unit_price' => 25,
    ],
    [
      'item_code' => 'xxxxx-xxx',
; This is converted to 1.2 in output. If we should change this to 1.20 instead,
; we should change the field type 'decimal' for this and other fields to be
; more specific and add formatting in validateFieldValue().
      'unit_price' => 1.20,
    ]
  ]
]
--
; The FbSales element is directly inside "Element", because there is only one.
; We assume AFAS could accept multiple elements in one call, in which case we
; "Element" would contain an array of elements; see FbSales-not-flattened.txt.
{
    "FbSales": {
        "Element": {
            "Fields": {
                "RfCs": "Weborder 123",
                "DbId": 25000,
                "CuId": "EUR",
                "Unit": 1,
                "War": "*** - ******",
                "OrDa": {TODAY}
            },
            "Objects": {
; By contrast, the FbSalesLines "Element" will always contain an array, even
; if there's only one line item. The UpdateObject class always outputs an array
; for embedded objects (object reference fields) which are allowed to contain
; multiple values.
                "FbSalesLines": {
                    "Element": [
                        {
                            "Fields": {
                                "ItCd": "xxxxx",
                                "QuUn": 5,
                                "Upri": 25,
                                "VaIt": 2,
                                "BiUn": "Stk"
                            }
                        },
                        {
                            "Fields": {
                                "ItCd": "xxxxx-xxx",
                                "Upri": 1.2,
                                "VaIt": 2,
                                "BiUn": "Stk",
                                "QuUn": 1
                            }
                        }
                    ]
                }
            }
        }
    }
}
--
; XML notation is older than JSON notation. Since (unlike JSON) it is possible
; for an object tag to contain multiple 'Element' tags, there is no difference
; in structure between a single or multiple elements.
<FbSales xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Element>
    <Fields Action="insert">
      <RfCs>Weborder 123</RfCs>
      <DbId>25000</DbId>
      <CuId>EUR</CuId>
      <Unit>1</Unit>
      <War>*** - ******</War>
      <OrDa>{TODAY}</OrDa>
    </Fields>
    <Objects>
      <FbSalesLines>
        <Element>
          <Fields Action="insert">
            <ItCd>xxxxx</ItCd>
            <QuUn>5</QuUn>
            <Upri>25</Upri>
            <VaIt>2</VaIt>
            <BiUn>Stk</BiUn>
          </Fields>
        </Element>
        <Element>
          <Fields Action="insert">
            <ItCd>xxxxx-xxx</ItCd>
            <Upri>1.2</Upri>
            <VaIt>2</VaIt>
            <BiUn>Stk</BiUn>
            <QuUn>1</QuUn>
          </Fields>
        </Element>
      </FbSalesLines>
    </Objects>
  </Element>
</FbSales>
