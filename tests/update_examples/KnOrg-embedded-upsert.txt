; This represents an insert of a new contact/person object inside an existing
; organisation object. If the organisation data/address does not change, the
; 'name' and 'address' fields/objects are not necessary.
; We always specify action "update" just to be sure, for XML updates; see
; KnOrg-embedded-update.txt.
; We need to specify DEFAULT_CHANGE | ALLOW_CHANGES to get In & SeNm populated
; and to split the house number off the street field.
KnOrganisation:update:55
[
  'code' => '1100000',
  'name' => 'Wyz',
  'contact' => [
    'email' => '<EMAIL>',
    'phone' => '06-22517218',
    'person' => [
      'first_name' => '<PERSON><PERSON><PERSON>',
      'last_name' => 'v. Muit',
    ],
  ],
]
; After creating the object with above data and "update" action, we need to set
; the embedded contact/organisation data to be an "insert" instead:
eval: $object->getObject('KnContact')->setAction('insert');
--
; Note: I have never tried this insert-inside-an-update in JSON/REST. It may
; work with the PUT 'endpoint' because of the MatchPer value - but someone will
; need to test this.
{
    "KnOrganisation": {
        "Element": {
            "Fields": {
; MatchOga is set to 0 by default, for most updates.
                "BcCo": "1100000",
                "Nm": "Wyz",
                "MatchOga": 0
            },
            "Objects": {
                "KnContact": {
                    "Element": {
                        "Fields": {
                            "TeNr": "06-22517218",
                            "EmAd": "<EMAIL>",
                            "ViKc": "PRS"
                        },
                        "Objects": {
                            "KnPerson": {
                                "Element": {
                                    "Fields": {
                                        "FiNm": "Roderik",
                                        "LaNm": "Muit",
                                        "Is": "v.",
                                        "In": "R.",
                                        "SeNm": "MUIT",
                                        "SpNm": false,
                                        "ViGe": "O",
                                        "Corr": false,
                                        "AutoNum": true,
; MatchPer has a default of 7 (for inserts).
                                        "MatchPer": 7
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
--
<KnOrganisation xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Element>
    <Fields Action="update">
      <BcCo>1100000</BcCo>
      <Nm>Wyz</Nm>
      <MatchOga>0</MatchOga>
    </Fields>
    <Objects>
      <KnContact>
        <Element>
          <Fields Action="insert">
            <TeNr>06-22517218</TeNr>
            <EmAd><EMAIL></EmAd>
            <ViKc>PRS</ViKc>
          </Fields>
          <Objects>
            <KnPerson>
              <Element>
                <Fields Action="insert">
                  <FiNm>Roderik</FiNm>
                  <LaNm>Muit</LaNm>
                  <Is>v.</Is>
                  <In>R.</In>
                  <SeNm>MUIT</SeNm>
                  <SpNm>0</SpNm>
                  <ViGe>O</ViGe>
                  <Corr>0</Corr>
                  <AutoNum>1</AutoNum>
                  <MatchPer>7</MatchPer>
                </Fields>
              </Element>
            </KnPerson>
          </Objects>
        </Element>
      </KnContact>
    </Objects>
  </Element>
</KnOrganisation>
