; An extra insert:
; - In the other object we can test that KnOrg.PbAd (postal_address_is_address)
;   = 1 and 'street' is split.
; - Here we test KnOrg.PbAd staying empty, plus adding BeginDate &
;   KnBasicAddress.PbAd (is_po_box) = 1; we do not split street name & person
;   last name, because we don't supply the ALLOW_CHANGE bit to output().
KnOrganisation:insert
[
  'name' => 'Wyz',
  'address' => [
    'street' => 'Govert Flinckstraat 168A',
    'zip_code' => '1072EP',
    'town' => 'Amsterdam',
    'country_iso' => 'NL',
; This should never be necessary; AFAS says the field is ignored on inserts.
; However, we still want to pass it to AFAS if code explicitly specifies it;
; basically we leave it up to AFAS whether or not to ignore the value.
    'BeginDate' => '2018-01-01',
  ],
  'postal_address' => [
; is_po_box is required, so it has a default of FALSE in our property
; definitions. Therefore we need to explicitly set it in case of a Postbus
; (or we need to pass ALLOW_CHANGE as a behavior bitmask value).
    'is_po_box' => true,
    'street' => 'Postbus 10881',
    'zip_code' => '1001 EW',
    'town' => 'Amsterdam',
; PT is not an AFAS code; resolves to P.
  'country_iso' => 'PT',
  ],
  'contact' => [
    'email' => '<EMAIL>',
    'phone' => '06-22517218',
    'person' => [
      'first_name' => 'Roderik',
      'last_name' => 'v. Muit',
    ],
  ],
]
--
{
    "KnOrganisation": {
        "Element": {
            "Fields": {
                "Nm": "Wyz",
                "AutoNum": true,
                "MatchOga": 6
            },
            "Objects": {
                "KnBasicAddressAdr": {
                    "Element": {
                        "Fields": {
                            "Ad": "Govert Flinckstraat 168A",
                            "ZpCd": "1072EP",
                            "Rs": "Amsterdam",
                            "BeginDate": "2018-01-01",
                            "CoId": "NL",
                            "PbAd": false,
                            "ResZip": false
                        }
                    }
                },
                "KnBasicAddressPad": {
                    "Element": {
                        "Fields": {
                            "PbAd": true,
                            "Ad": "Postbus 10881",
                            "ZpCd": "1001 EW",
                            "Rs": "Amsterdam",
                            "CoId": "P",
                            "ResZip": false
                        }
                    }
                },
                "KnContact": {
                    "Element": {
                        "Fields": {
                            "TeNr": "06-22517218",
                            "EmAd": "<EMAIL>",
                            "ViKc": "PRS"
                        },
                        "Objects": {
                            "KnPerson": {
                                "Element": {
                                    "Fields": {
                                        "FiNm": "Roderik",
                                        "LaNm": "v. Muit",
                                        "SpNm": false,
                                        "ViGe": "O",
                                        "Corr": false,
                                        "AutoNum": true,
                                        "MatchPer": 7
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
--
<KnOrganisation xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Element>
    <Fields Action="insert">
      <Nm>Wyz</Nm>
      <AutoNum>1</AutoNum>
      <MatchOga>6</MatchOga>
    </Fields>
    <Objects>
      <KnBasicAddressAdr>
        <Element>
          <Fields Action="insert">
            <Ad>Govert Flinckstraat 168A</Ad>
            <ZpCd>1072EP</ZpCd>
            <Rs>Amsterdam</Rs>
            <BeginDate>2018-01-01</BeginDate>
            <CoId>NL</CoId>
            <PbAd>0</PbAd>
            <ResZip>0</ResZip>
          </Fields>
        </Element>
      </KnBasicAddressAdr>
      <KnBasicAddressPad>
        <Element>
          <Fields Action="insert">
            <PbAd>1</PbAd>
            <Ad>Postbus 10881</Ad>
            <ZpCd>1001 EW</ZpCd>
            <Rs>Amsterdam</Rs>
            <CoId>P</CoId>
            <ResZip>0</ResZip>
          </Fields>
        </Element>
      </KnBasicAddressPad>
      <KnContact>
        <Element>
          <Fields Action="insert">
            <TeNr>06-22517218</TeNr>
            <EmAd><EMAIL></EmAd>
            <ViKc>PRS</ViKc>
          </Fields>
          <Objects>
            <KnPerson>
              <Element>
                <Fields Action="insert">
                  <FiNm>Roderik</FiNm>
                  <LaNm>v. Muit</LaNm>
                  <SpNm>0</SpNm>
                  <ViGe>O</ViGe>
                  <Corr>0</Corr>
                  <AutoNum>1</AutoNum>
                  <MatchPer>7</MatchPer>
                </Fields>
              </Element>
            </KnPerson>
          </Objects>
        </Element>
      </KnContact>
    </Objects>
  </Element>
</KnOrganisation>
