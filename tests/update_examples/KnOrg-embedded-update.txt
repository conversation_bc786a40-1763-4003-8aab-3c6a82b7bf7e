; A note: one would expect that the presence of a MatchOga/MatchPer of 0 and
; BcCo numbers would make it clear that we are updating all objects. However,
; last time I tested (feb 2015), the XML UpdateConnector threw an error "Object
; variable or With block variable not set" when I did not explicitly specify
; action "update". This is a Visual Basic error, pointing to an error in AFAS'
; program code.
; My takeaway: always specify the $action parameter for SOAP/XML, so the
; "Action" XML attributes are always present.
; We need to specify DEFAULT_CHANGE | ALLOW_CHANGES to get In & SeNm populated
; and to split the house number off the street field
; We need to specify DEFAULT_CHANGE | ALLOW_CHANGES | ALLOW_REFORMAT_PHONE_NR
; to get In & SeNm populated, split the house number off the street field (and
; populate PbAd; and reformat the Dutch phone number.
KnOrganisation:update:1079
[
  'code' => '1100000',
  'name' => 'Wyz',
; One thing to keep in mind when updating address: the postal_address_is_address
; field (KnOrganisation.PbAd or KnContact/KnPerson.PadAdr) is not set
; automatically for updates, because we have no good way of deriving whether
; we need to update just this one address or also the postal address. The caller
; should take this into account.
  'address' => [
    'street' => 'Govert Flinckstraat 168A',
    'zip_code' => '1072EP',
    'town' => 'Amsterdam',
    'country_iso' => 'NL',
  ],
  'contact' => [
    'email' => '<EMAIL>',
    'person' => [
      'code' => '100000',
      'first_name' => 'Roderik',
      'last_name' => 'Muit',
; A note: I have never actually tried sending a phone number into here because
; I've always put it inside the contact (see insert.txt). But the code at least
; supports it.
      'phone' => '**********',
    ],
  ],
]
--
{
    "KnOrganisation": {
        "Element": {
            "Fields": {
                "BcCo": "1100000",
                "Nm": "Wyz",
; MatchOga is set to 0 by default, for most updates. If BcCo is not set, that
; would generate an AFAS error, which is on purpose. (See code comments.)
                "MatchOga": 0
            },
            "Objects": {
                "KnBasicAddressAdr": {
                    "Element": {
                        "Fields": {
                            "Ad": "Govert Flinckstraat",
                            "ZpCd": "1072EP",
                            "Rs": "Amsterdam",
                            "CoId": "NL",
                            "HmNr": 168,
                            "HmAd": "A",
; PbAd = false is added automatically - not because it's the default value but
; because when ALLOW_CHANGES is specified, PbAd is derived from "Ad" on updates.
                            "PbAd": false,
                            "BeginDate": {TODAY}
                        }
                    }
                },
                "KnContact": {
                    "Element": {
                        "Fields": {
                            "EmAd": "<EMAIL>"
                        },
                        "Objects": {
                            "KnPerson": {
                                "Element": {
                                    "Fields": {
                                        "BcCo": "100000",
                                        "FiNm": "Roderik",
                                        "LaNm": "Muit",
                                        "TeNr": "06-22517218",
; Also "Is" is explicitly set to empty, on updates with ALLOW_CHANGES specified.
                                        "Is": "",
                                        "In": "R.",
                                        "SeNm": "MUIT",
; MatchPer is set to 0 by default, for most updates. If BcCo is not set, that
; would generate an AFAS error, which is on purpose. (See code comments.)
                                        "MatchPer": 0
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
--
<KnOrganisation xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Element>
    <Fields Action="update">
      <BcCo>1100000</BcCo>
      <Nm>Wyz</Nm>
      <MatchOga>0</MatchOga>
    </Fields>
    <Objects>
      <KnBasicAddressAdr>
        <Element>
          <Fields Action="update">
            <Ad>Govert Flinckstraat</Ad>
            <ZpCd>1072EP</ZpCd>
            <Rs>Amsterdam</Rs>
            <CoId>NL</CoId>
            <HmNr>168</HmNr>
            <HmAd>A</HmAd>
            <PbAd>0</PbAd>
            <BeginDate>{TODAY}</BeginDate>
          </Fields>
        </Element>
      </KnBasicAddressAdr>
      <KnContact>
        <Element>
          <Fields Action="update">
            <EmAd><EMAIL></EmAd>
          </Fields>
          <Objects>
            <KnPerson>
              <Element>
                <Fields Action="update">
                  <BcCo>100000</BcCo>
                  <FiNm>Roderik</FiNm>
                  <LaNm>Muit</LaNm>
                  <TeNr>06-22517218</TeNr>
                  <Is></Is>
                  <In>R.</In>
                  <SeNm>MUIT</SeNm>
                  <MatchPer>0</MatchPer>
                </Fields>
              </Element>
            </KnPerson>
          </Objects>
        </Element>
      </KnContact>
    </Objects>
  </Element>
</KnOrganisation>
