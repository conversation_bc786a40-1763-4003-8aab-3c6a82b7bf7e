; As a comparison to FbSales.txt (and a test for the argument), we specify
; DEFAULT_CHANGE & ^FLATTEN_SINGLE_ELEMENT which has effect on JSON output.
; The non-flattened version (with "Element" being an array) is also how output
; always looks if there is more than a single element; see e.g. KnSubject.txt.
FbSales:insert:22
[
  'reference' => 'Weborder 123',
  'order_date' => '2018-12-08',
  'debtor_id' => 25000,
  'currency_code' => 'EUR',
  'warehouse' => '*** - ******',
; We just made "unit" an alias for "Unit", to prevent confusion...
  'Unit' => '1',
  'line_items' => [
    [
      'item_code' => 'xxxxx',
      'quantity' => '5',
      'unit_price' => 25,
    ],
    [
      'item_code' => 'xxxxx-xxx',
      'unit_price' => 1.20,
    ]
  ]
]
--
{
    "FbSales": {
        "Element": [
            {
                "Fields": {
                    "RfCs": "Weborder 123",
                    "OrDa": "2018-12-08",
                    "DbId": 25000,
                    "CuId": "EUR",
                    "Unit": 1,
                    "War": "*** - ******"
                },
                "Objects": {
                    "FbSalesLines": {
                        "Element": [
                            {
                                "Fields": {
                                    "ItCd": "xxxxx",
                                    "QuUn": 5,
                                    "Upri": 25,
                                    "VaIt": 2,
                                    "BiUn": "Stk"
                                }
                            },
                            {
                                "Fields": {
                                    "ItCd": "xxxxx-xxx",
                                    "Upri": 1.2,
                                    "VaIt": 2,
                                    "BiUn": "Stk",
                                    "QuUn": 1
                                }
                            }
                        ]
                    }
                }
            }
        ]
    }
}
--
<FbSales xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Element>
    <Fields Action="insert">
      <RfCs>Weborder 123</RfCs>
      <OrDa>2018-12-08</OrDa>
      <DbId>25000</DbId>
      <CuId>EUR</CuId>
      <Unit>1</Unit>
      <War>*** - ******</War>
    </Fields>
    <Objects>
      <FbSalesLines>
        <Element>
          <Fields Action="insert">
            <ItCd>xxxxx</ItCd>
            <QuUn>5</QuUn>
            <Upri>25</Upri>
            <VaIt>2</VaIt>
            <BiUn>Stk</BiUn>
          </Fields>
        </Element>
        <Element>
          <Fields Action="insert">
            <ItCd>xxxxx-xxx</ItCd>
            <Upri>1.2</Upri>
            <VaIt>2</VaIt>
            <BiUn>Stk</BiUn>
            <QuUn>1</QuUn>
          </Fields>
        </Element>
      </FbSalesLines>
    </Objects>
  </Element>
</FbSales>
