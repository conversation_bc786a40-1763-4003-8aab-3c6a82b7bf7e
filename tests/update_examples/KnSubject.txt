KnSubject
; For 'id properties' we can specify either '@id' or '@SbId'.
[
  [ '@id' =>   1957, 'type' => 1, 'description' => 'öndèrwérp', ],
  [ '@SbId' => 1958, 'type' => 2, 'description' => 'öndèrwérp twee', ],
]
--
; "Element" is an array of 2 elements here. If only one element were provided,
; it would be on the first level inside "Element", instead of being wrapped in
; an array. (This is because FLATTEN_SINGLE_ELEMENT is part of the default
; bitmask value of the output($change_behavior) parameter. If
; FLATTEN_SINGLE_ELEMENT is turned off, the wrapper array will be kept also for
; a single element.)
{
    "KnSubject": {
        "Element": [
            {
                "@SbId": 1957,
                "Fields": {
                    "StId": 1,
                    "Ds": "\u00f6nd\u00e8rw\u00e9rp"
                }
            },
            {
                "@SbId": 1958,
                "Fields": {
                    "StId": 2,
                    "Ds": "\u00f6nd\u00e8rw\u00e9rp twee"
                }
            }
        ]
    }
}
--
<KnSubject xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Element SbId="1957">
    <Fields>
      <StId>1</StId>
      <Ds>öndèrwérp</Ds>
    </Fields>
  </Element>
  <Element SbId="1958">
    <Fields>
      <StId>2</StId>
      <Ds>öndèrwérp twee</Ds>
    </Fields>
  </Element>
</KnSubject>
