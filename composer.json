{"name": "opjeplaatsman/omnikassa-sdk", "type": "library", "license": "MIT", "description": "Rabobank OmniKassa SDK for PHP", "keywords": ["rabobank", "omni", "kassa"], "authors": [{"name": "Rabobank"}], "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "platform": {"php": "8.1"}}, "minimum-stability": "stable", "require": {"php": ">= 8.1", "ext-json": "*", "guzzlehttp/guzzle": "^7.0", "netresearch/jsonmapper": "^4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.1", "phake/phake": "^4.1", "phpunit/phpunit": "^9.5"}, "autoload": {"psr-4": {"nl\\rabobank\\gict\\payments_savings\\omnikassa_sdk\\": "src"}}, "autoload-dev": {"psr-4": {"nl\\rabobank\\gict\\payments_savings\\omnikassa_sdk\\test\\": "test"}}, "archive": {"exclude": [".idea/*", "azure-pipelines.yml", ".git", ".giti<PERSON>re", "rabobank-omnikassa-sdk-*.tar"]}}